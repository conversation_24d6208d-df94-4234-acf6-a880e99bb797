# Table of Contents

- [Project Architecture](#project-architecture)

  - [Database](#database)
  - [Hosting](#hosting)
  - [Domains](#domains)
  - [DNS](#dns)
  - [Emails](#emails)

- [Branches & releases](#branches-and-releases)

- [Project Technologies](#project-technologies)

- [Coding Conventions](#coding-conventions)

# Project architecture

## Database

Supabase used. Credentials are taken from `.env` file, where:

- for local development, the file encloses local Supabase instance config
- for staging, the file is overriden by GitHub Actions with `bizzu-dev` credentials
- for production, the file is overriden by GitHub Actions with `bizzu-prod` credentials

### For local development use local Supabase instance, Docker-enclosed.

Read more at https://supabase.com/docs/guides/local-development.

#### Most importantly:

- We develop using a local Supabase instance (with schema based on `supabase/migrations`)
- If your changes require schema modifications, you must commit the migration file(s) in `supabase/migrations/`
- When something changes in `supabase/`, merging to `develop` will automatically update the `bizzu-dev` database with new migration files (via GitHub Actions)
- Similarly for `main` branch, but for `bizzu-prod` database.

#### Initial setup:

- install Docker (find appropriate installer in https://supabase.com/docs/guides/local-development)
- install Supabase CLI:

```
  $ cd supabase/
  $ npm i
```

#### Then, for daily development:

ℹ️ Run all below commands from `supabase/` directory.

- to start local db instance:

```
$ npm run db:start
```

- to stop local db instance:

```
$ npm run db:stop
```

- to generate migration file from local db schema changes (generate migration file for the schema changes you have added):

```
$ npm run db:diff
```

The migration files will be generated in `supabase/migrations` directory, and should be commited to the repo.

- to apply migrations to local db instance (apply new schema changes from origin to your local instance):

```
$ npm run db:push
```

- to reset local db instance to the state of the commited schema (e.g., in case of breaking changes, when you need to clear whole db (data-included) and want to re-create schema from scratch):

```
$ npm run db:reset
```

IF logs are needed for debugging the query within database you can add one of those methods in postgres function. Then in logs it will be visible

```sql
-- General information
    RAISE LOG 'SOME INFO: %', some_variable;

-- Warnings
    RAISE WARNING 'SOME WARNING: %', some_variable;
```

## Hosting

Static files are hosted using Firebase Hosting, two separate projects are used, each featuring two sites:

- `bizzu-dev` (development) with sites:
  - `bizzu-dev` (lading page)
  - `bizzu-client-dev` (client app)
- `bizzu` (production) with sites:
  - `bizzu` (lading page)
  - `bizzu-client-f5a0c` (client app)

## Domains

namecheap.com is our domain provider. Two domains are used:

- `bizzu.dev` (development)
- `bizzu.app` (production)

## DNS

To handle dynamic subdomain routing, Cloudflare is used. Whole traffic goes through Cloudflare.

The important part of this is `cloudflare/worker.js` file, which is defined in Cloudflare as a worker. It is responsible for routing the traffic to the correct Firebase site based on the subdomain/environment.

## Emails

[Brevo.com](https://www.brevo.com/) is used for sending emails.
Currently, a free plan, with 300 emails/day.
It is connected to Supabase, and is used for sending Auth-related emails.

# Branches and releases

Two branches are used:

- `main` - production, protected, whatever lands here is deployed to production (https://bizzu.app)
- `develop` - development, protected, requires pull requests, whatever lands here is deployed to staging environment (https://bizzu.dev)

# Project technologies

- Vite
- React (with TanStack Router)
- shadcn (UI elements)

# Coding conventions

- file naming: camelCase, e.g., app.ts, myComponent.module.scss
