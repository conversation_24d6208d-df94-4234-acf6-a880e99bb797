import React, { ButtonHTMLAttributes } from "react";
import classNames from "classnames";
import styles from "./button.module.scss";

export function PrimaryButton({
  className,
  children,
  onClick,
  disabled,
  type,
}: {
  className?: string;
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  type?: ButtonHTMLAttributes<HTMLButtonElement>["type"];
}) {
  return (
    <button
      className={classNames(styles.primaryButton, className)}
      onClick={onClick}
      disabled={disabled}
      type={type}
    >
      {children}
    </button>
  );
}

export function SecondaryButton({
  className,
  children,
  disabled,
  href = "#",
  onClick,
}: {
  className?: string;
  children: React.ReactNode;
  disabled?: boolean;
  href?: string;
  onClick?: () => void;
}) {
  const Element = href ? "a" : "button";

  return (
    <Element
      className={classNames(styles.secondaryButton, className)}
      href={href}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </Element>
  );
}
