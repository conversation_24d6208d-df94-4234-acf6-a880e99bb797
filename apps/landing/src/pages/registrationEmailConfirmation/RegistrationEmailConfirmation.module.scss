.pageContainer {
  min-height: 60vh;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
}

.pageContent {
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
  padding: 2rem;
}

.iconContainer {
  margin-bottom: 2rem;
}

.emailIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

h2 {
  color: #333;
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 2rem;
}

.messageContainer {
  margin-bottom: 2rem;
}

.mainMessage {
  font-size: 1.1rem;
  color: #555;
  margin-bottom: 1rem;
}

.emailAddress {
  background-color: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  margin: 1.5rem 0;
  font-size: 1.1rem;
  color: #007acc;
  word-break: break-all;
}

.instructionText {
  font-size: 1rem;
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.5;
}

.helpText {
  background-color: #f0f8ff;
  border-left: 4px solid #007acc;
  border-radius: 4px;
  padding: 1.5rem;
  margin: 2rem 0;
  text-align: left;

  p {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
  }

  ul {
    margin: 0;
    padding-left: 1.5rem;
    color: #555;

    li {
      margin-bottom: 0.5rem;
      line-height: 1.4;

      &:last-child {
        font-weight: 600;
        color: #007acc;
        background-color: rgba(0, 122, 204, 0.1);
        padding: 0.5rem;
        border-radius: 4px;
        margin-top: 0.75rem;
        list-style: none;
        margin-left: -1.5rem;
        padding-left: 1.5rem;
      }
    }
  }
}

.resendMessage {
  margin: 1.5rem 0;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid;
  font-weight: 500;

  &.error {
    background-color: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
  }

  &.success {
    background-color: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
  }
}

.actionButtons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 2rem 0;
  align-items: center;

  @media (min-width: 480px) {
    flex-direction: row;
    justify-content: center;
    gap: 1.5rem;
  }
}

.resendButton {
  font-size: 1rem;
  padding: 0.75rem 2rem;
  min-width: 200px;
  transition: all 0.3s ease;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;

    &:hover {
      transform: none;
    }
  }
}

.backButton {
  font-size: 0.9rem;
  padding: 0.5rem 1.5rem;
  text-decoration: none;

  &:hover {
    text-decoration: none;
  }
}

.contactInfo {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid #e9ecef;
  font-size: 0.9rem;
  color: #666;

  a {
    color: #007acc;
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }
}

@media (max-width: 768px) {
  .pageContent {
    padding: 1.5rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  .emailIcon {
    font-size: 3rem;
  }

  .helpText {
    padding: 1rem;
  }
}
