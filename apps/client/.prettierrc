{"arrowParens": "always", "bracketSpacing": true, "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxBracketSameLine": false, "jsxSingleQuote": false, "printWidth": 120, "proseWrap": "always", "quoteProps": "as-needed", "requirePragma": false, "semi": true, "singleQuote": false, "tabWidth": 4, "trailingComma": "all", "useTabs": false, "plugins": ["@ianvs/prettier-plugin-sort-imports", "prettier-plugin-tailwindcss"], "importOrder": ["^react$", "<THIRD_PARTY_MODULES>", "^@/", "^\\./", "^\\.\\./", "\\.(s)?css$"], "importOrderSeparation": "none", "importOrderSortSpecifiers": true}