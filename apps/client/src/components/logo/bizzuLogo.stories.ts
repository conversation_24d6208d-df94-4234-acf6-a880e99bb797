import type { <PERSON>a, StoryObj } from "@storybook/react-vite";
import { B<PERSON>zu<PERSON><PERSON> } from "./bizzuLogo";

const meta = {
    title: "Components/Logo/BizzuLogo",
    component: BizzuLogo,
    parameters: {
        layout: "centered",
    },
    tags: ["autodocs"],
    argTypes: {
        size: {
            control: { type: "select" },
            options: ["small", "medium", "large"],
        },
        width: {
            control: { type: "number" },
        },
        height: {
            control: { type: "number" },
        },
    },
} satisfies Meta<typeof BizzuLogo>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    args: {},
};

export const Small: Story = {
    args: {
        size: "small",
    },
};

export const Medium: Story = {
    args: {
        size: "medium",
    },
};

export const Large: Story = {
    args: {
        size: "large",
    },
};

export const CustomSize: Story = {
    args: {
        width: 400,
        height: 100,
    },
};
