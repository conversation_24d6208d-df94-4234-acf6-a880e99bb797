import { useState } from "react";
import { Eye, EyeOff } from "lucide-react";
import type { FieldValues } from "react-hook-form";
import { Adornment, Text, type TextFieldProps } from "./text/text";

type PasswordFieldProps<T extends FieldValues> = Omit<TextFieldProps<T>, "endAdornment" | "password">;

export function Password<T extends FieldValues>({ name, label, startAdornment, ...rest }: PasswordFieldProps<T>) {
    const [showPassword, setShowPassword] = useState(false);

    const toggleShowPassword = () => setShowPassword((prev) => !prev);
    const endAdornment = showPassword ? (
        <Eye onClick={toggleShowPassword} className="h-4 w-4" />
    ) : (
        <EyeOff onClick={toggleShowPassword} className="h-4 w-4" />
    );
    const inputType = showPassword ? "text" : "password";

    return (
        <Text
            {...rest}
            name={name}
            label={label}
            startAdornment={startAdornment}
            endAdornment={<Adornment interactive>{endAdornment}</Adornment>}
            type={inputType}
        />
    );
}
