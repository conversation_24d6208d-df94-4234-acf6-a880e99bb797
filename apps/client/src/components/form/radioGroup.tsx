import type { FieldValues } from "react-hook-form";
import { Controller } from "react-hook-form";
import { RadioGroupItem, RadioGroup as UiRadioGroup } from "@/components/ui/radio-group";
import { useFormContext } from "./formContext";
import { FormField } from "./formField";
import type { BaseFormFieldProps } from "./types";

type Option<T = string> = {
    label: string;
    value: T;
};

type RadioGroupProps<T extends FieldValues> = BaseFormFieldProps<T> & {
    options: Option[];
} & Omit<React.ComponentPropsWithoutRef<typeof UiRadioGroup>, "value" | "onValueChange">;

export function RadioGroup<T extends FieldValues>({ name, label, options, ...rest }: RadioGroupProps<T>) {
    const { control } = useFormContext();

    return (
        <Controller
            name={name}
            control={control}
            render={({ field, fieldState }) => (
                <FormField label={label} error={fieldState.error?.message}>
                    <UiRadioGroup {...rest} value={field.value} onValueChange={field.onChange} className="gap-3">
                        {options.map((opt) => (
                            <div key={opt.value} className="flex items-center gap-2">
                                <RadioGroupItem id={`${String(name)}-${String(opt.value)}`} value={opt.value} />
                                <label htmlFor={`${String(name)}-${String(opt.value)}`} className="text-sm">
                                    {opt.label}
                                </label>
                            </div>
                        ))}
                    </UiRadioGroup>
                </FormField>
            )}
        />
    );
}
