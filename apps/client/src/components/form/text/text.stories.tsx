import type { <PERSON>a, StoryObj } from "@storybook/react-vite";
import { User } from "lucide-react";
import { z } from "zod/v4";
import { Text } from "./text";
import { Form } from "../form";

type MockFormData = {
    firstName: string;
    lastName: string;
};

const mockSchema = z.object({
    firstName: z.string().min(3, "Username must be at least 3 characters").optional(),
    lastName: z.string().min(3, "Username must be at least 3 characters").optional(),
});

const meta = {
    title: "Form/Text",
    component: Text,
    parameters: {
        layout: "centered",
    },
    argTypes: {},
} satisfies Meta<typeof Text<MockFormData>>;

export default meta;
type Story = StoryObj<typeof meta>;

function Example({ title, children }: { title: string; children: React.ReactNode }) {
    return (
        <div className="mb-5 border-1 p-5">
            <h3 className="mb-4 text-lg font-semibold">{title}</h3>
            {children}
        </div>
    );
}

export const AllVariants: Story = {
    args: {
        name: "firstName" as keyof MockFormData,
        label: "Edit Mode Comparison",
    },
    render: () => (
        <div className="w-80 space-y-8">
            <Example title="Classic Edit Mode">
                <Form
                    editMode="classic"
                    defaultValues={{
                        firstName: "Bartosz",
                        lastName: "Kowalskikwenrfngnwnegurjbngjenbgqeugbrequbhkjbhrfqnbjkreqhthjwtyjry",
                    }}
                    schema={mockSchema}
                    onSubmit={() => {
                        // Empty function
                    }}
                >
                    <div className="grid gap-4">
                        <Text<MockFormData>
                            name="firstName"
                            label="First name"
                            placeholder="Enter your firstName"
                            startAdornment={<User className="h-4 w-4" />}
                        />
                        <Text<MockFormData> name="lastName" label="Last name" placeholder="Enter your lastName" />
                    </div>
                </Form>
            </Example>

            <Example title="Inline Edit Mode - Editable">
                <Form
                    editMode="inline"
                    defaultValues={{
                        firstName: "Bartosz",
                        lastName: "Kowalskikwenrfngnwnegurjbngjenbgqeugbrequbhkjbhrfqnbjkreqhthjwtyjry",
                    }}
                    schema={mockSchema}
                    onSubmit={(...args) => console.log("form submit", args)}
                >
                    <div className="grid gap-4">
                        <Text<MockFormData>
                            name="firstName"
                            label="First name"
                            placeholder="Twoje imie"
                            startAdornment={<User className="h-4 w-4" />}
                            endAdornment={<User className="h-4 w-4" />}
                        />
                        <Text<MockFormData> name="lastName" label="Last name" placeholder="Enter your lastName" />
                    </div>
                </Form>
            </Example>

            <Example title="Inline Edit Mode - Readonly">
                <Form
                    editMode="inline"
                    defaultValues={{
                        firstName: "Bartosz",
                        lastName: "Kowalskikwenrfngnwnegurjbngjenbgqeugbrequbhkjbhrfqnbjkreqhthjwtyjry",
                    }}
                    schema={mockSchema}
                    onSubmit={() => {
                        // Empty function
                    }}
                >
                    <div className="grid w-full gap-4">
                        <Text<MockFormData>
                            name="firstName"
                            label="First name"
                            placeholder="Double-click to select"
                            readonly
                            startAdornment={<User className="h-4 w-4" />}
                        />
                        <Text<MockFormData>
                            name="lastName"
                            label="Last name"
                            placeholder="Enter your lastName"
                            readonly
                        />
                    </div>
                </Form>
            </Example>
        </div>
    ),
};
