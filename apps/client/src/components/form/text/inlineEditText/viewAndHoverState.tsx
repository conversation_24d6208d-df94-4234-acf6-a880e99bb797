import { Edit3 } from "lucide-react";
import { type ControllerRenderProps, type FieldValues } from "react-hook-form";
import TextEllipsis from "@/components/textEllipsis";
import { cn } from "@/utils/classname";
import type { BaseFormFieldProps } from "../../types";

export type ViewAndHoverStateProps<T extends FieldValues> = Omit<BaseFormFieldProps<T>, "name" | "label"> & {
    startAdornment?: React.ReactNode;
    endAdornment?: React.ReactNode;
    field: ControllerRenderProps<T>;
    onClick: () => void;
    displayText: string;
    readonly?: boolean;
} & React.InputHTMLAttributes<HTMLInputElement>;

export default function ViewAndHoverState<T extends FieldValues>({
    startAdornment,
    endAdornment,
    field,
    onClick,
    readonly,
    displayText,
}: ViewAndHoverStateProps<T>) {
    return (
        <div
            className={cn(
                `group border-b-border/60 relative -mb-0.5 rounded-md border border-transparent py-2 pl-3 text-sm transition-colors`,
                !readonly && "hover:bg-muted/50 hover:border-border cursor-pointer hover:pr-3",
            )}
            onClick={!readonly ? onClick : undefined}
        >
            <div className="flex items-center justify-between">
                <div className="flex min-w-0 items-center gap-2">
                    {startAdornment && (
                        <div className="text-muted-foreground relative -top-[1px] -left-[1px] flex flex-shrink-0 items-center">
                            {startAdornment}
                        </div>
                    )}
                    <div className={field.value ? "text-foreground min-w-0" : "text-muted-foreground"}>
                        <TextEllipsis>{displayText}</TextEllipsis>
                    </div>
                    {endAdornment && (
                        <div className="text-muted-foreground flex flex-shrink-0 items-center group-hover:hidden">
                            {endAdornment}
                        </div>
                    )}
                    <div className={cn(readonly ? "hidden" : "hidden group-hover:block")}>
                        <Edit3 className="size-4 text-purple-700" />
                    </div>
                </div>
            </div>
        </div>
    );
}
