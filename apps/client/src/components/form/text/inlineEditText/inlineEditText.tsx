import { useState } from "react";
import { type ControllerRenderProps, type FieldValues } from "react-hook-form";
import EditState from "./editiState";
import ViewAndHoverState from "./viewAndHoverState";
import type { BaseFormFieldProps } from "../../types";

export type InlineEditTextFieldProps<T extends FieldValues> = Omit<BaseFormFieldProps<T>, "name" | "label"> & {
    startAdornment?: React.ReactNode;
    endAdornment?: React.ReactNode;
    placeholder?: string;
} & React.InputHTMLAttributes<HTMLInputElement>;

export default function InlineEditText<T extends FieldValues>({
    startAdornment,
    endAdornment,
    placeholder,
    field,
    readonly,
}: InlineEditTextFieldProps<T> & { field: ControllerRenderProps<T> }) {
    const [isEditing, setIsEditing] = useState(false);
    const [originalValue, setOriginalValue] = useState<string>("");

    const enterEditModeHandler = () => {
        if (readonly) return;

        setIsEditing(true);
        setOriginalValue(field.value || "");
    };

    const displayText = field.value || placeholder || "-";

    if (isEditing) {
        return (
            <EditState
                originalValue={originalValue}
                field={field}
                startAdornment={startAdornment}
                onEditDone={() => setIsEditing(false)}
            />
        );
    }

    return (
        <ViewAndHoverState
            field={field}
            startAdornment={startAdornment}
            endAdornment={endAdornment}
            onClick={enterEditModeHandler}
            displayText={displayText}
            readonly={readonly}
        />
    );
}
