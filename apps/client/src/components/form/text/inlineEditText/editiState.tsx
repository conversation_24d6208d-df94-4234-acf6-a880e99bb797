import { useState } from "react";
import { useClickAway } from "@uidotdev/usehooks";
import { Check, X } from "lucide-react";
import { type ControllerRenderProps, type FieldValues } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { useFormSubmit } from "@/utils/form";
import { useFormContext } from "../../formContext";
import type { BaseFormFieldProps } from "../../types";
import ClassicEditText from "../classicEditText";
import { Adornment } from "../text";

export type EditStateProps<T extends FieldValues> = Omit<BaseFormFieldProps<T>, "name" | "label"> & {
    originalValue: string;
    onEditDone: () => void;
    startAdornment?: React.ReactNode;
} & React.InputHTMLAttributes<HTMLInputElement>;

export default function EditState<T extends FieldValues>({
    originalValue,
    field,
    startAdornment,
    onEditDone,
}: EditStateProps<T> & { field: ControllerRenderProps<T> }) {
    const [isSaving, setIsSaving] = useState(false);
    const { errors } = useFormContext<T>();

    function handleCancel() {
        field.onChange(originalValue);
        onEditDone();
    }

    function handleSave() {
        if (isSaving) return;

        if (field.value === originalValue) {
            onEditDone();

            return;
        }

        setIsSaving(true);

        try {
            submitForm();

            if (!errors || Object.keys(errors).length === 0) {
                onEditDone();
            }
        } finally {
            setIsSaving(false);
        }
    }

    const ref = useClickAway(() => {
        handleSave();
    });

    const [, submitForm] = useFormSubmit(ref as React.RefObject<HTMLElement | null>);

    function handleKeyDown(e: React.KeyboardEvent) {
        if (e.key === "Enter") {
            e.preventDefault();
            handleSave();
        } else if (e.key === "Escape") {
            handleCancel();
        }
    }

    return (
        <div ref={ref as React.RefObject<HTMLDivElement>}>
            <ClassicEditText
                field={field}
                onKeyDown={handleKeyDown}
                autoFocus
                startAdornment={startAdornment}
                endAdornment={
                    <Adornment interactive>
                        <Button
                            type="button"
                            size="sm"
                            variant="ghost"
                            onClick={handleSave}
                            disabled={isSaving}
                            className="h-7 w-7 p-0 text-green-700!"
                        >
                            <Check className="h-3 w-3" />
                        </Button>
                        <Button
                            type="button"
                            size="sm"
                            variant="ghost"
                            onClick={handleCancel}
                            disabled={isSaving}
                            className="h-7 w-7 p-0 text-red-700!"
                        >
                            <X className="h-3 w-3" />
                        </Button>
                    </Adornment>
                }
            />
        </div>
    );
}
