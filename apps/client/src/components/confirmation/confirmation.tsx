import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { type ButtonVariant } from "@/components/ui/button";

type Props = {
    title: React.ReactNode;
    description: React.ReactNode;
    children: React.ReactNode;
    onConfirm: () => void;
    onCancel?: () => void;
    actionButtonVariant?: ButtonVariant["variant"];
    actionButtonText?: string;
    cancelButtonText?: string;
};

export function Confirmation({
    title,
    description,
    children,
    onConfirm,
    onCancel,
    actionButtonVariant,
    actionButtonText,
    cancelButtonText,
}: Props) {
    return (
        <AlertDialog>
            <AlertDialogTrigger asChild>{children}</AlertDialogTrigger>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>{title}</AlertDialogTitle>
                    <AlertDialogDescription>{description}</AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel onClick={onCancel}>{cancelButtonText}</AlertDialogCancel>
                    <AlertDialogAction onClick={onConfirm} variant={actionButtonVariant}>
                        {actionButtonText}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
}
