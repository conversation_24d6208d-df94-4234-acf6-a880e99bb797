import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react-vite";
import { Separator } from "./separator";

const meta = {
    title: "UI/Separator",
    component: Separator,
    parameters: {
        layout: "centered",
    },
    tags: ["autodocs"],
    argTypes: {
        orientation: {
            control: { type: "select" },
            options: ["horizontal", "vertical"],
        },
        decorative: {
            control: { type: "boolean" },
        },
    },
} satisfies Meta<typeof Separator>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Horizontal: Story = {
    render: () => (
        <div className="w-64">
            <div className="space-y-1">
                <h4 className="text-sm leading-none font-medium">Radix Primitives</h4>
                <p className="text-muted-foreground text-sm">An open-source UI component library.</p>
            </div>
            <Separator className="my-4" />
            <div className="flex h-5 items-center space-x-4 text-sm">
                <div>Blog</div>
                <Separator orientation="vertical" />
                <div>Docs</div>
                <Separator orientation="vertical" />
                <div>Source</div>
            </div>
        </div>
    ),
    args: {
        orientation: "horizontal",
    },
};

export const Vertical: Story = {
    render: () => (
        <div className="flex h-5 items-center space-x-4 text-sm">
            <div>Blog</div>
            <Separator orientation="vertical" />
            <div>Docs</div>
            <Separator orientation="vertical" />
            <div>Source</div>
        </div>
    ),
    args: {
        orientation: "vertical",
    },
};

export const InCard: Story = {
    render: () => (
        <div className="w-80 rounded-lg border p-6">
            <div className="space-y-1">
                <h3 className="text-lg font-semibold">Settings</h3>
                <p className="text-muted-foreground text-sm">Manage your account settings and preferences.</p>
            </div>
            <Separator className="my-4" />
            <div className="space-y-4">
                <div>
                    <h4 className="text-sm font-medium">Profile</h4>
                    <p className="text-muted-foreground text-sm">Update your profile information.</p>
                </div>
                <Separator />
                <div>
                    <h4 className="text-sm font-medium">Security</h4>
                    <p className="text-muted-foreground text-sm">Manage your security settings.</p>
                </div>
                <Separator />
                <div>
                    <h4 className="text-sm font-medium">Notifications</h4>
                    <p className="text-muted-foreground text-sm">Configure your notification preferences.</p>
                </div>
            </div>
        </div>
    ),
};

export const InNavigation: Story = {
    render: () => (
        <nav className="flex items-center space-x-4 text-sm font-medium">
            <a href="#" className="hover:text-foreground/80 transition-colors">
                Home
            </a>
            <Separator orientation="vertical" className="h-4" />
            <a href="#" className="hover:text-foreground/80 transition-colors">
                About
            </a>
            <Separator orientation="vertical" className="h-4" />
            <a href="#" className="hover:text-foreground/80 transition-colors">
                Services
            </a>
            <Separator orientation="vertical" className="h-4" />
            <a href="#" className="hover:text-foreground/80 transition-colors">
                Contact
            </a>
        </nav>
    ),
};
