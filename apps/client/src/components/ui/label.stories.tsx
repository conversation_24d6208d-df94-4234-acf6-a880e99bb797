import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react-vite";
import { Checkbox } from "./checkbox";
import { Input } from "./input";
import { Label } from "./label";

const meta = {
    title: "UI/Label",
    component: Label,
    parameters: {
        layout: "centered",
    },
    tags: ["autodocs"],
} satisfies Meta<typeof Label>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    args: {
        children: "Label text",
    },
};

export const WithInput: Story = {
    render: () => (
        <div className="grid w-full max-w-sm items-center gap-1.5">
            <Label htmlFor="email">Email</Label>
            <Input type="email" id="email" placeholder="Email" />
        </div>
    ),
};

export const WithCheckbox: Story = {
    render: () => (
        <div className="flex items-center space-x-2">
            <Checkbox id="terms" />
            <Label htmlFor="terms">Accept terms and conditions</Label>
        </div>
    ),
};

export const Required: Story = {
    render: () => (
        <div className="grid w-full max-w-sm items-center gap-1.5">
            <Label htmlFor="required-field">
                Required Field
                <span className="text-destructive ml-1">*</span>
            </Label>
            <Input type="text" id="required-field" placeholder="This field is required" />
        </div>
    ),
};

export const WithDescription: Story = {
    render: () => (
        <div className="grid w-full max-w-sm items-center gap-1.5">
            <Label htmlFor="password">Password</Label>
            <Input type="password" id="password" placeholder="Enter your password" />
            <p className="text-muted-foreground text-xs">Must be at least 8 characters long.</p>
        </div>
    ),
};

export const Disabled: Story = {
    render: () => (
        <div className="grid w-full max-w-sm items-center gap-1.5">
            <Label htmlFor="disabled-input" className="opacity-50">
                Disabled Field
            </Label>
            <Input type="text" id="disabled-input" placeholder="Disabled input" disabled />
        </div>
    ),
};
