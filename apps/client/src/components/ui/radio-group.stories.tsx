import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react-vite";
import { Label } from "./label";
import { RadioGroup, RadioGroupItem } from "./radio-group";

const meta = {
    title: "UI/RadioGroup",
    component: RadioGroup,
    parameters: {
        layout: "centered",
    },
    tags: ["autodocs"],
    argTypes: {
        defaultValue: {
            control: { type: "text" },
        },
        disabled: {
            control: { type: "boolean" },
        },
    },
} satisfies Meta<typeof RadioGroup>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    render: () => (
        <RadioGroup defaultValue="option-one">
            <div className="flex items-center space-x-2">
                <RadioGroupItem value="option-one" id="option-one" />
                <Label htmlFor="option-one">Option One</Label>
            </div>
            <div className="flex items-center space-x-2">
                <RadioGroupItem value="option-two" id="option-two" />
                <Label htmlFor="option-two">Option Two</Label>
            </div>
        </RadioGroup>
    ),
};

export const WithDescriptions: Story = {
    render: () => (
        <RadioGroup defaultValue="comfortable">
            <div className="flex items-center space-x-2">
                <RadioGroupItem value="default" id="r1" />
                <div className="grid gap-1.5 leading-none">
                    <Label htmlFor="r1">Default</Label>
                    <p className="text-muted-foreground text-xs">The default option for most users.</p>
                </div>
            </div>
            <div className="flex items-center space-x-2">
                <RadioGroupItem value="comfortable" id="r2" />
                <div className="grid gap-1.5 leading-none">
                    <Label htmlFor="r2">Comfortable</Label>
                    <p className="text-muted-foreground text-xs">A more spacious layout with extra padding.</p>
                </div>
            </div>
            <div className="flex items-center space-x-2">
                <RadioGroupItem value="compact" id="r3" />
                <div className="grid gap-1.5 leading-none">
                    <Label htmlFor="r3">Compact</Label>
                    <p className="text-muted-foreground text-xs">A dense layout to fit more content.</p>
                </div>
            </div>
        </RadioGroup>
    ),
};

export const Disabled: Story = {
    render: () => (
        <RadioGroup defaultValue="option-one" disabled>
            <div className="flex items-center space-x-2">
                <RadioGroupItem value="option-one" id="disabled-one" />
                <Label htmlFor="disabled-one">Option One (Disabled)</Label>
            </div>
            <div className="flex items-center space-x-2">
                <RadioGroupItem value="option-two" id="disabled-two" />
                <Label htmlFor="disabled-two">Option Two (Disabled)</Label>
            </div>
        </RadioGroup>
    ),
};

export const SingleDisabled: Story = {
    render: () => (
        <RadioGroup defaultValue="option-one">
            <div className="flex items-center space-x-2">
                <RadioGroupItem value="option-one" id="single-one" />
                <Label htmlFor="single-one">Option One</Label>
            </div>
            <div className="flex items-center space-x-2">
                <RadioGroupItem value="option-two" id="single-two" disabled />
                <Label htmlFor="single-two">Option Two (Disabled)</Label>
            </div>
            <div className="flex items-center space-x-2">
                <RadioGroupItem value="option-three" id="single-three" />
                <Label htmlFor="single-three">Option Three</Label>
            </div>
        </RadioGroup>
    ),
};

export const PaymentMethod: Story = {
    render: () => (
        <div className="w-[350px]">
            <h3 className="mb-4 text-lg font-medium">Payment Method</h3>
            <RadioGroup defaultValue="card">
                <div className="flex items-center space-x-2">
                    <RadioGroupItem value="card" id="card" />
                    <div className="grid gap-1.5 leading-none">
                        <Label htmlFor="card">Credit Card</Label>
                        <p className="text-muted-foreground text-xs">Pay with your credit or debit card</p>
                    </div>
                </div>
                <div className="flex items-center space-x-2">
                    <RadioGroupItem value="paypal" id="paypal" />
                    <div className="grid gap-1.5 leading-none">
                        <Label htmlFor="paypal">PayPal</Label>
                        <p className="text-muted-foreground text-xs">Pay with your PayPal account</p>
                    </div>
                </div>
                <div className="flex items-center space-x-2">
                    <RadioGroupItem value="apple" id="apple" />
                    <div className="grid gap-1.5 leading-none">
                        <Label htmlFor="apple">Apple Pay</Label>
                        <p className="text-muted-foreground text-xs">Pay with Touch ID or Face ID</p>
                    </div>
                </div>
            </RadioGroup>
        </div>
    ),
};
