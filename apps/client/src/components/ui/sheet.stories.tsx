import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react-vite";
import { Menu } from "lucide-react";
import { But<PERSON> } from "./button";
import { Input } from "./input";
import { Label } from "./label";
import {
    Sheet,
    SheetClose,
    SheetContent,
    SheetD<PERSON><PERSON>,
    She<PERSON><PERSON><PERSON>er,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    She<PERSON><PERSON>rigger,
} from "./sheet";

const meta = {
    title: "UI/Sheet",
    component: Sheet,
    parameters: {
        layout: "centered",
    },
    tags: ["autodocs"],
} satisfies Meta<typeof Sheet>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    render: () => (
        <Sheet>
            <SheetTrigger asChild>
                <Button variant="outline">Open Sheet</Button>
            </SheetTrigger>
            <SheetContent>
                <SheetHeader>
                    <SheetTitle>Edit profile</SheetTitle>
                    <SheetDescription>Make changes to your profile here. Click save when you're done.</SheetDescription>
                </SheetHeader>
                <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="name" className="text-right">
                            Name
                        </Label>
                        <Input id="name" value="Pedro Duarte" className="col-span-3" />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="username" className="text-right">
                            Username
                        </Label>
                        <Input id="username" value="@peduarte" className="col-span-3" />
                    </div>
                </div>
                <SheetFooter>
                    <SheetClose asChild>
                        <Button type="submit">Save changes</Button>
                    </SheetClose>
                </SheetFooter>
            </SheetContent>
        </Sheet>
    ),
};

export const LeftSide: Story = {
    render: () => (
        <Sheet>
            <SheetTrigger asChild>
                <Button variant="outline">
                    <Menu className="mr-2 h-4 w-4" />
                    Menu
                </Button>
            </SheetTrigger>
            <SheetContent side="left">
                <SheetHeader>
                    <SheetTitle>Navigation</SheetTitle>
                    <SheetDescription>Navigate through the application.</SheetDescription>
                </SheetHeader>
                <div className="grid gap-4 py-4">
                    <Button variant="ghost" className="justify-start">
                        Dashboard
                    </Button>
                    <Button variant="ghost" className="justify-start">
                        Projects
                    </Button>
                    <Button variant="ghost" className="justify-start">
                        Team
                    </Button>
                    <Button variant="ghost" className="justify-start">
                        Settings
                    </Button>
                </div>
            </SheetContent>
        </Sheet>
    ),
};

export const TopSide: Story = {
    render: () => (
        <Sheet>
            <SheetTrigger asChild>
                <Button variant="outline">Open from top</Button>
            </SheetTrigger>
            <SheetContent side="top">
                <SheetHeader>
                    <SheetTitle>Notifications</SheetTitle>
                    <SheetDescription>Your recent notifications and updates.</SheetDescription>
                </SheetHeader>
                <div className="grid gap-4 py-4">
                    <div className="flex items-center space-x-4">
                        <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                        <div>
                            <p className="text-sm font-medium">New message received</p>
                            <p className="text-muted-foreground text-xs">2 minutes ago</p>
                        </div>
                    </div>
                    <div className="flex items-center space-x-4">
                        <div className="h-2 w-2 rounded-full bg-green-500"></div>
                        <div>
                            <p className="text-sm font-medium">Task completed</p>
                            <p className="text-muted-foreground text-xs">1 hour ago</p>
                        </div>
                    </div>
                    <div className="flex items-center space-x-4">
                        <div className="h-2 w-2 rounded-full bg-yellow-500"></div>
                        <div>
                            <p className="text-sm font-medium">System update available</p>
                            <p className="text-muted-foreground text-xs">3 hours ago</p>
                        </div>
                    </div>
                </div>
            </SheetContent>
        </Sheet>
    ),
};

export const BottomSide: Story = {
    render: () => (
        <Sheet>
            <SheetTrigger asChild>
                <Button variant="outline">Open from bottom</Button>
            </SheetTrigger>
            <SheetContent side="bottom">
                <SheetHeader>
                    <SheetTitle>Quick Actions</SheetTitle>
                    <SheetDescription>Perform quick actions from here.</SheetDescription>
                </SheetHeader>
                <div className="grid grid-cols-2 gap-4 py-4">
                    <Button>Create New</Button>
                    <Button variant="outline">Import</Button>
                    <Button variant="outline">Export</Button>
                    <Button variant="outline">Share</Button>
                </div>
            </SheetContent>
        </Sheet>
    ),
};

export const WithForm: Story = {
    render: () => (
        <Sheet>
            <SheetTrigger asChild>
                <Button>Add Contact</Button>
            </SheetTrigger>
            <SheetContent>
                <SheetHeader>
                    <SheetTitle>Add New Contact</SheetTitle>
                    <SheetDescription>Fill in the details to add a new contact to your list.</SheetDescription>
                </SheetHeader>
                <div className="grid gap-4 py-4">
                    <div className="grid gap-2">
                        <Label htmlFor="firstName">First Name</Label>
                        <Input id="firstName" placeholder="Enter first name" />
                    </div>
                    <div className="grid gap-2">
                        <Label htmlFor="lastName">Last Name</Label>
                        <Input id="lastName" placeholder="Enter last name" />
                    </div>
                    <div className="grid gap-2">
                        <Label htmlFor="email">Email</Label>
                        <Input id="email" type="email" placeholder="Enter email address" />
                    </div>
                    <div className="grid gap-2">
                        <Label htmlFor="phone">Phone</Label>
                        <Input id="phone" type="tel" placeholder="Enter phone number" />
                    </div>
                </div>
                <SheetFooter>
                    <SheetClose asChild>
                        <Button variant="outline">Cancel</Button>
                    </SheetClose>
                    <SheetClose asChild>
                        <Button>Add Contact</Button>
                    </SheetClose>
                </SheetFooter>
            </SheetContent>
        </Sheet>
    ),
};
