import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import { Apple, Banana, Cherry, Grape } from "lucide-react";
import { Label } from "./label";
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectLabel,
    SelectSeparator,
    SelectTrigger,
    SelectValue,
} from "./select";

const meta = {
    title: "UI/Select",
    component: Select,
    parameters: {
        layout: "centered",
    },
    tags: ["autodocs"],
} satisfies Meta<typeof Select>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    render: () => (
        <Select>
            <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select a fruit" />
            </SelectTrigger>
            <SelectContent>
                <SelectItem value="apple">Apple</SelectItem>
                <SelectItem value="banana">Banana</SelectItem>
                <SelectItem value="cherry">Cherry</SelectItem>
                <SelectItem value="grape">Grape</SelectItem>
            </SelectContent>
        </Select>
    ),
};

export const WithLabel: Story = {
    render: () => (
        <div className="grid w-full max-w-sm items-center gap-1.5">
            <Label htmlFor="fruit-select">Choose a fruit</Label>
            <Select>
                <SelectTrigger className="w-[180px]" id="fruit-select">
                    <SelectValue placeholder="Select a fruit" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="apple">Apple</SelectItem>
                    <SelectItem value="banana">Banana</SelectItem>
                    <SelectItem value="cherry">Cherry</SelectItem>
                    <SelectItem value="grape">Grape</SelectItem>
                </SelectContent>
            </Select>
        </div>
    ),
};

export const WithGroups: Story = {
    render: () => (
        <Select>
            <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select a fruit" />
            </SelectTrigger>
            <SelectContent>
                <SelectGroup>
                    <SelectLabel>Fruits</SelectLabel>
                    <SelectItem value="apple">Apple</SelectItem>
                    <SelectItem value="banana">Banana</SelectItem>
                    <SelectItem value="cherry">Cherry</SelectItem>
                </SelectGroup>
                <SelectSeparator />
                <SelectGroup>
                    <SelectLabel>Vegetables</SelectLabel>
                    <SelectItem value="carrot">Carrot</SelectItem>
                    <SelectItem value="broccoli">Broccoli</SelectItem>
                    <SelectItem value="spinach">Spinach</SelectItem>
                </SelectGroup>
            </SelectContent>
        </Select>
    ),
};

export const WithIcons: Story = {
    render: () => (
        <Select>
            <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select a fruit" />
            </SelectTrigger>
            <SelectContent>
                <SelectItem value="apple">
                    <Apple className="mr-2 h-4 w-4" />
                    Apple
                </SelectItem>
                <SelectItem value="banana">
                    <Banana className="mr-2 h-4 w-4" />
                    Banana
                </SelectItem>
                <SelectItem value="cherry">
                    <Cherry className="mr-2 h-4 w-4" />
                    Cherry
                </SelectItem>
                <SelectItem value="grape">
                    <Grape className="mr-2 h-4 w-4" />
                    Grape
                </SelectItem>
            </SelectContent>
        </Select>
    ),
};

export const SmallSize: Story = {
    render: () => (
        <Select>
            <SelectTrigger className="w-[150px]" size="sm">
                <SelectValue placeholder="Size" />
            </SelectTrigger>
            <SelectContent>
                <SelectItem value="xs">Extra Small</SelectItem>
                <SelectItem value="sm">Small</SelectItem>
                <SelectItem value="md">Medium</SelectItem>
                <SelectItem value="lg">Large</SelectItem>
                <SelectItem value="xl">Extra Large</SelectItem>
            </SelectContent>
        </Select>
    ),
};

export const WithDisabledItems: Story = {
    render: () => (
        <Select>
            <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select an option" />
            </SelectTrigger>
            <SelectContent>
                <SelectItem value="option1">Option 1</SelectItem>
                <SelectItem value="option2" disabled>
                    Option 2 (Disabled)
                </SelectItem>
                <SelectItem value="option3">Option 3</SelectItem>
                <SelectItem value="option4" disabled>
                    Option 4 (Disabled)
                </SelectItem>
                <SelectItem value="option5">Option 5</SelectItem>
            </SelectContent>
        </Select>
    ),
};

export const LongList: Story = {
    render: () => (
        <Select>
            <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select a country" />
            </SelectTrigger>
            <SelectContent>
                {[
                    "United States",
                    "Canada",
                    "Mexico",
                    "United Kingdom",
                    "France",
                    "Germany",
                    "Italy",
                    "Spain",
                    "Netherlands",
                    "Belgium",
                    "Switzerland",
                    "Austria",
                    "Sweden",
                    "Norway",
                    "Denmark",
                    "Finland",
                    "Poland",
                    "Czech Republic",
                    "Hungary",
                    "Romania",
                    "Bulgaria",
                    "Greece",
                    "Portugal",
                    "Ireland",
                    "Luxembourg",
                ].map((country) => (
                    <SelectItem key={country} value={country.toLowerCase().replace(/\s+/g, "-")}>
                        {country}
                    </SelectItem>
                ))}
            </SelectContent>
        </Select>
    ),
};
