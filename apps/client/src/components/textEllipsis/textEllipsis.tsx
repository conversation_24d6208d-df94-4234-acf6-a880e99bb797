import { useEffect, useRef, useState } from "react";
import { Tooltip, TooltipContent, TooltipTrigger } from "../ui/tooltip";

type TextEllipsisProps = {
    children: React.ReactNode;
};

export default function TextEllipsis({ children }: TextEllipsisProps) {
    const textRef = useRef<HTMLDivElement>(null);
    const [isOverflowing, setIsOverflowing] = useState(false);

    useEffect(() => {
        const checkOverflow = () => {
            if (textRef.current) {
                setIsOverflowing(textRef.current.scrollWidth > textRef.current.clientWidth);
            }
        };

        checkOverflow();
        window.addEventListener("resize", checkOverflow);

        return () => window.removeEventListener("resize", checkOverflow);
    }, [children]);

    const content = (
        <div ref={textRef} className="overflow-hidden text-ellipsis whitespace-nowrap">
            {children}
        </div>
    );

    if (!isOverflowing) {
        return content;
    }

    return (
        <Tooltip>
            <TooltipTrigger asChild>{content}</TooltipTrigger>
            <TooltipContent>
                <p className="max-w-full">{children}</p>
            </TooltipContent>
        </Tooltip>
    );
}
