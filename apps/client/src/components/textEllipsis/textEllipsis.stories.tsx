import type { Meta, StoryObj } from "@storybook/react-vite";
import TextEllipsis from "./textEllipsis";

const meta = {
    title: "Components/TextEllipsis",
    component: TextEllipsis,
    parameters: {
        layout: "centered",
    },
    tags: ["autodocs"],
    argTypes: {
        children: {
            control: { type: "text" },
            description: "The content to display with ellipsis functionality",
        },
    },
} satisfies Meta<typeof TextEllipsis>;

export default meta;
type Story = StoryObj<typeof meta>;

export const MultipleInstances: Story = {
    args: {
        children: "Default text content",
    },
    render: () => (
        <div className="space-y-4">
            <div className="w-32 border border-gray-300 p-2">
                <TextEllipsis>lalalalalalalalalalalalallalalalalalalalalalalalalalalalalallalalalala</TextEllipsis>
            </div>
            <div className="w-48 border border-gray-300 p-2">
                <TextEllipsis>Second instance with even longer text that should also show ellipsis</TextEllipsis>
            </div>
            <div className="w-64 border border-gray-300 p-2">
                <TextEllipsis>Third instance</TextEllipsis>
            </div>
        </div>
    ),
    parameters: {
        docs: {
            description: {
                story: "Multiple TextEllipsis components with different container widths",
            },
        },
    },
};
