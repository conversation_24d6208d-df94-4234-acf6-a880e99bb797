import React from "react";
import { Link } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";
import {
    Breadcrumb as BreadcrumbComponent,
    BreadcrumbEllipsis,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { FileRouteTypes } from "@/routeTree.gen";

export type Breadcrumb = {
    label: string;
    to: FileRouteTypes["to"];
    isActive: boolean;
};

interface BreadcrumbsProps {
    breadcrumbs: Breadcrumb[];
}

export function Breadcrumbs({ breadcrumbs }: BreadcrumbsProps) {
    const { t } = useTranslation();
    const shouldShowDropdown = breadcrumbs.length > 5;

    if (!shouldShowDropdown) {
        return (
            <BreadcrumbComponent>
                <BreadcrumbList>
                    {breadcrumbs.map((breadcrumb, index) => (
                        <React.Fragment key={breadcrumb.to}>
                            <BreadcrumbItem className="text-xl">
                                <BreadcrumbLink asChild>
                                    <Link
                                        to={breadcrumb.to}
                                        className={
                                            breadcrumb.isActive
                                                ? "text-foreground"
                                                : "text-muted-foreground hover:text-foreground"
                                        }
                                    >
                                        {breadcrumb.label}
                                    </Link>
                                </BreadcrumbLink>
                            </BreadcrumbItem>
                            {index < breadcrumbs.length - 1 && <BreadcrumbSeparator />}
                        </React.Fragment>
                    ))}
                </BreadcrumbList>
            </BreadcrumbComponent>
        );
    }

    const firstBreadcrumb = breadcrumbs[0];
    const middleBreadcrumbs = breadcrumbs.slice(1, breadcrumbs.length - 3);
    const lastBreadcrumbs = breadcrumbs.slice(-3);

    return (
        <BreadcrumbComponent>
            <BreadcrumbList>
                <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                        <Link
                            to={firstBreadcrumb.to}
                            className={
                                firstBreadcrumb.isActive
                                    ? "text-foreground font-medium"
                                    : "text-muted-foreground hover:text-foreground"
                            }
                        >
                            {firstBreadcrumb.label}
                        </Link>
                    </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />

                <BreadcrumbItem>
                    <DropdownMenu>
                        <DropdownMenuTrigger className="flex items-center gap-1">
                            <BreadcrumbEllipsis className="size-4" />
                            <span className="sr-only">{t("toggleMenu")}</span>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="start">
                            {middleBreadcrumbs.map((breadcrumb) => (
                                <DropdownMenuItem key={breadcrumb.to} asChild>
                                    <Link
                                        to={breadcrumb.to}
                                        className={
                                            breadcrumb.isActive
                                                ? "text-foreground font-medium"
                                                : "text-muted-foreground hover:text-foreground"
                                        }
                                    >
                                        {breadcrumb.label}
                                    </Link>
                                </DropdownMenuItem>
                            ))}
                        </DropdownMenuContent>
                    </DropdownMenu>
                </BreadcrumbItem>
                <BreadcrumbSeparator />

                {lastBreadcrumbs.map((breadcrumb, index) => (
                    <React.Fragment key={breadcrumb.to}>
                        <BreadcrumbItem>
                            <BreadcrumbLink asChild>
                                <Link
                                    to={breadcrumb.to}
                                    className={
                                        breadcrumb.isActive
                                            ? "text-foreground font-medium"
                                            : "text-muted-foreground hover:text-foreground"
                                    }
                                >
                                    {breadcrumb.label}
                                </Link>
                            </BreadcrumbLink>
                        </BreadcrumbItem>
                        {index < lastBreadcrumbs.length - 1 && <BreadcrumbSeparator />}
                    </React.Fragment>
                ))}
            </BreadcrumbList>
        </BreadcrumbComponent>
    );
}
