import {
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
    type ColumnDef,
} from "@tanstack/react-table";
import { Table, TableBody, TableHeader, TableRow } from "@/components/ui/table";
import { ColumnHeader } from "./components/columnHeader";
import { DataTablePagination } from "./components/pagination";
import { TableBodyContent } from "./components/tableBody";
import type { ColumnFiltersState, PaginationState, SortingState } from "./types";

interface DataGridProps<TData> {
    columns: ColumnDef<TData>[];
    data: TData[];
    isLoading?: boolean;
    pagination?: PaginationState;
    sorting?: SortingState;
    columnFilters?: ColumnFiltersState;
    onRowClick?: (row: TData) => void;
}

export function DataGrid<TData>({
    columns,
    data,
    isLoading,
    pagination,
    sorting,
    columnFilters,
    onRowClick,
}: DataGridProps<TData>) {
    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: pagination ? getPaginationRowModel() : undefined,
        getSortedRowModel: sorting ? getSortedRowModel() : undefined,
        getFilteredRowModel: columnFilters ? getFilteredRowModel() : undefined,
        defaultColumn: {
            enableSorting: false,
            enableHiding: false,
            enableColumnFilter: false,
        },
        onSortingChange: sorting ? sorting[1] : undefined,
        onPaginationChange: pagination ? pagination[1] : undefined,
        onColumnFiltersChange: columnFilters ? columnFilters[1] : undefined,
        state: {
            pagination: pagination ? pagination[0] : undefined,
            sorting: sorting ? sorting[0] : undefined,
            columnFilters: columnFilters ? columnFilters[0] : undefined,
        },
    });

    return (
        <div className="flex h-full flex-col">
            <div className="min-h-0 flex-1 overflow-hidden">
                <Table>
                    <TableHeader className="bg-background sticky top-0 z-1">
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id} className="hover:bg-inherit">
                                {headerGroup.headers.map((header) => {
                                    return <ColumnHeader key={header.id} header={header} />;
                                })}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody className="h-full overflow-auto">
                        <TableBodyContent
                            table={table}
                            numberOfColumns={columns.length}
                            isLoading={isLoading}
                            onRowClick={onRowClick}
                        />
                    </TableBody>
                </Table>
            </div>
            {pagination && (
                <div className="shrink-0 py-2">
                    <DataTablePagination table={table} />
                </div>
            )}
        </div>
    );
}
