import { type ColumnDef } from "@tanstack/react-table";
import { t } from "i18next";
import { Checkbox } from "@/components/ui/checkbox";

export function createSelectColumn<T>(
    options?: Omit<ColumnDef<T>, "id" | "header" | "cell" | "enableSorting" | "enableHiding">,
): ColumnDef<T> {
    return {
        ...options,
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
                onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                aria-label={t("dataGrid.selectAll")}
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value) => row.toggleSelected(!!value)}
                aria-label={t("dataGrid.selectRow")}
            />
        ),
        enableSorting: false,
        enableHiding: false,
    };
}
