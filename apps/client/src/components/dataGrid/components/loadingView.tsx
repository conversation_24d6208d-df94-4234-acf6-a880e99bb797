import { Skeleton } from "@/components/ui/skeleton";
import { TableCell, TableRow } from "@/components/ui/table";

type LoadingViewProps = {
    numberOfColumns: number;
};

export function LoadingView({ numberOfColumns }: LoadingViewProps) {
    return Array.from({ length: 5 }).map((_, rowIndex) => (
        <TableRow key={`loading-row-${rowIndex}`}>
            {Array.from({ length: numberOfColumns }).map((_, cellIndex) => (
                <TableCell key={`loading-cell-${rowIndex}-${cellIndex}`}>
                    <Skeleton className="h-12 w-full rounded" />
                </TableCell>
            ))}
        </TableRow>
    ));
}
