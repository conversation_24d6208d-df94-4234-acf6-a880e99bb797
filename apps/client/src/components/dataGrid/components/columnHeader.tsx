import { flexRender, type Header } from "@tanstack/react-table";
import { ArrowDown, ArrowUp, ChevronsUpDown } from "lucide-react";
import { TableHead } from "@/components/ui/table";

interface ColumnHeaderProps<TData, TValue> {
    header: Header<TData, TValue>;
}

export function ColumnHeader<TData, TValue>({ header }: ColumnHeaderProps<TData, TValue>) {
    const column = header.column;

    if (header.isPlaceholder) {
        return null;
    }

    if (!column.getCanSort()) {
        return (
            <TableHead>
                <span className="text-foreground font-semibold">
                    {flexRender(column.columnDef.header, header.getContext())}
                </span>
            </TableHead>
        );
    }

    return (
        <TableHead onClick={() => column.toggleSorting()} className="cursor-pointer">
            <div className="flex items-center gap-1">
                <span className="text-foreground font-semibold">
                    {flexRender(column.columnDef.header, header.getContext())}
                </span>
                {column.getIsSorted() === "desc" ? (
                    <ArrowDown className="size-4" />
                ) : column.getIsSorted() === "asc" ? (
                    <ArrowUp className="size-4" />
                ) : (
                    <ChevronsUpDown className="size-4" />
                )}
            </div>
        </TableHead>
    );
}
