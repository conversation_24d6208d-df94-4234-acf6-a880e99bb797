import { flexRender, type Table as TanstackTable } from "@tanstack/react-table";
import { TableCell, TableRow } from "@/components/ui/table";
import { cn } from "@/utils";
import { EmptyView } from "./emptyView";
import { LoadingView } from "./loadingView";

type Props<T> = {
    table: TanstackTable<T>;
    numberOfColumns: number;
    isLoading?: boolean;
    onRowClick?: (row: T) => void;
    emptyView?: React.ReactNode;
};

export function TableBodyContent<T>({
    table,
    numberOfColumns,
    isLoading,
    onRowClick,
    emptyView = <EmptyView />,
}: Props<T>) {
    if (isLoading) {
        return <LoadingView numberOfColumns={numberOfColumns} />;
    }

    const isEmpty = table.getRowModel().rows?.length === 0;

    if (isEmpty) {
        return (
            <TableRow>
                <TableCell colSpan={numberOfColumns} className="h-6">
                    {emptyView}
                </TableCell>
            </TableRow>
        );
    }

    return (
        <>
            {table.getRowModel().rows.map((row) => (
                <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                    onClick={onRowClick ? () => onRowClick(row.original) : undefined}
                    className={cn(onRowClick && "cursor-pointer")}
                >
                    {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                    ))}
                </TableRow>
            ))}
        </>
    );
}
