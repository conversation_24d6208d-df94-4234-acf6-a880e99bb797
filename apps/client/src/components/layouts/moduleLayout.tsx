function Layout({ children }: { children: React.ReactNode }) {
    return (
        <div className="flex h-full flex-col gap-4 overflow-hidden p-4" data-slot="module-layout">
            {children}
        </div>
    );
}

function ModuleLayoutHeader({ children }: { children: React.ReactNode }) {
    return (
        <div className="flex shrink-0 items-center justify-between" data-slot="module-layout-header">
            {children}
        </div>
    );
}

function ModuleLayoutContent({ children }: { children: React.ReactNode }) {
    return (
        <div className="min-h-0 flex-1 overflow-auto" data-slot="module-layout-content">
            {children}
        </div>
    );
}

export const ModuleLayout = Object.assign(Layout, {
    Header: ModuleLayoutHeader,
    Content: ModuleLayoutContent,
});
