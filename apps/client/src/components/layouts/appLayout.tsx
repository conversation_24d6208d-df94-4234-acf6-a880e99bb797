import React from "react";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { getCookie } from "@/utils/cookie";
import { Sidebar } from "../navigation";

export function AppLayout({ children, showSidebar = true }: { children: React.ReactNode; showSidebar?: boolean }) {
    const sidebarState = getCookie("sidebar_state") === "true";

    if (!showSidebar) {
        return <div className="h-screen">{children}</div>;
    }

    return (
        <div className="h-screen">
            <SidebarProvider className="flex h-full flex-col" defaultOpen={sidebarState}>
                <div className="flex min-h-0 flex-1">
                    <Sidebar />
                    <SidebarInset>{children}</SidebarInset>
                </div>
            </SidebarProvider>
        </div>
    );
}
