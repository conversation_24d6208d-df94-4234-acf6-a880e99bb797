import { Link } from "@tanstack/react-router";
import { LogOut, PanelLeftIcon, User as UserIcon } from "lucide-react";
import { useTranslation } from "react-i18next";
import { useGetUser } from "@/api/auth";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useSidebar } from "@/components/ui/sidebar";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { useSignOut } from "@/hooks/useSignOut";

export default function UserAvatar() {
    const { t } = useTranslation();
    const { data } = useGetUser();
    const signOut = useSignOut();
    const { toggleSidebar } = useSidebar();

    const avatarLetter = data?.email?.charAt(0).toUpperCase() || "?";

    return (
        <DropdownMenu>
            <Tooltip>
                <TooltipTrigger>
                    <DropdownMenuTrigger asChild>
                        <Avatar className="cursor-pointer rounded-lg text-white">
                            <AvatarFallback className="bg-electric-violet-700">{avatarLetter}</AvatarFallback>
                        </Avatar>
                    </DropdownMenuTrigger>
                </TooltipTrigger>
                <TooltipContent side="right">{t("moreActions")}</TooltipContent>
            </Tooltip>
            <DropdownMenuContent className="min-w-56 rounded-lg" align="end" sideOffset={4}>
                <DropdownMenuGroup>
                    <DropdownMenuItem asChild>
                        <Link to="/users/$id" params={{ id: data?.id ?? "" }}>
                            <UserIcon />
                            {t("navigation.myProfile")}
                        </Link>
                    </DropdownMenuItem>
                </DropdownMenuGroup>

                <DropdownMenuItem onClick={() => signOut()}>
                    <LogOut />
                    {t("logout")}
                </DropdownMenuItem>

                <DropdownMenuItem className="md:hidden" onClick={() => toggleSidebar()}>
                    <PanelLeftIcon />
                    {t("navigation.showMenu")}
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
