import { <PERSON>bar<PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>oot<PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, Sidebar as UISidebar } from "@/components/ui/sidebar";
import Logo from "./logo";
import { Navigation } from "./navigation";
import { SidebarTrigger } from "./sidebarTrigger";
import UserAvatar from "./userAvatar";

export function Sidebar() {
    return (
        <UISidebar className="h-full" variant="sidebar" collapsible="icon">
            <SidebarHeader>
                <Logo />
            </SidebarHeader>
            <SidebarContent>
                <Navigation />
            </SidebarContent>
            <SidebarFooter className="hidden md:flex">
                <SidebarTrigger />
                <UserAvatar />
            </SidebarFooter>
        </UISidebar>
    );
}
