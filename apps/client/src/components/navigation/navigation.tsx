import { Link, useRouterState } from "@tanstack/react-router";
import { Home, Settings, Users } from "lucide-react";
import { useTranslation } from "react-i18next";
import { SidebarGroup, SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar } from "@/components/ui/sidebar";
import { cn } from "@/utils/classname";

export function Navigation() {
    const { t } = useTranslation();
    const location = useRouterState({ select: (s) => s.location.pathname });
    const { isMobile, toggleSidebar } = useSidebar();

    const items = [
        { to: "/", label: t("navigation.home"), Icon: Home },
        { to: "/users", label: t("navigation.users"), Icon: Users },
        { to: "/settings", label: t("navigation.settings"), Icon: Settings },
    ];

    return (
        <SidebarGroup>
            <SidebarMenu>
                {items.map((item) => {
                    const isActive = item.to === "/" ? location === item.to : location.startsWith(item.to);

                    return (
                        <SidebarMenuItem key={item.label}>
                            <SidebarMenuButton
                                asChild
                                isActive={isActive}
                                className={cn("hover:bg:-gray-500 text-gray-900", isActive && "bg-gray-500 font-bold")}
                                tooltip={item.label}
                                onClick={() => {
                                    if (isMobile) {
                                        toggleSidebar();
                                    }
                                }}
                            >
                                <Link to={item.to}>
                                    <item.Icon className={cn(isActive && "text-electric-violet-700")} />
                                    <span>{item.label}</span>
                                </Link>
                            </SidebarMenuButton>
                        </SidebarMenuItem>
                    );
                })}
            </SidebarMenu>
        </SidebarGroup>
    );
}
