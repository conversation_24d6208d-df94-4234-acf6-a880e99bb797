import { useTranslation } from "react-i18next";
import { SidebarTrigger as UiSidebarTrigger, useSidebar } from "@/components/ui/sidebar";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";

export function SidebarTrigger() {
    const { state } = useSidebar();
    const { t } = useTranslation();

    const isExpanded = state === "expanded";

    return (
        <Tooltip>
            <TooltipTrigger asChild>
                <UiSidebarTrigger className="cursor-pointer" />
            </TooltipTrigger>
            <TooltipContent side="right">{isExpanded ? t("collapseSidebar") : t("expandSidebar")}</TooltipContent>
        </Tooltip>
    );
}
