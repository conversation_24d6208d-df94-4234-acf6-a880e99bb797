import type { AuthError } from "@supabase/supabase-js";
import { Link as RouterLink } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";
import { z } from "zod/v4";
import { Form, Password, Text } from "@/components/form";
import { Notification } from "@/components/notification";
import { Button } from "@/components/ui/button";
import { links } from "@/utils/links";

export type LoginForm = {
    email: string;
    password: string;
};

const schema: z.ZodSchema<LoginForm> = z.object({
    email: z.email(),
    password: z.string().min(2),
});

type Props = {
    onLogin: (data: LoginForm) => void;
    error: AuthError | null;
    isPending?: boolean;
};

export default function LoginForm({ onLogin, error, isPending }: Props) {
    const { t } = useTranslation();

    return (
        <Form schema={schema} onSubmit={onLogin} defaultValues={{ password: "", email: "" }}>
            <div className="flex flex-col gap-4">
                {error && (
                    <Notification type="error">{t("loginForm.logInError", { context: error?.code })}</Notification>
                )}
                <Text<LoginForm>
                    name="email"
                    label={t("loginForm.email")}
                    placeholder={t("loginForm.emailPlaceholder")}
                    enablePasswordManagers
                />
                <Password<LoginForm>
                    name="password"
                    label={t("loginForm.password")}
                    placeholder={t("loginForm.passwordPlaceholder")}
                    enablePasswordManagers
                />
                <div className="flex justify-end">
                    <RouterLink to="/forgot-password" className="text-primary text-sm hover:underline">
                        {t("loginForm.forgotPassword")}
                    </RouterLink>
                </div>
                <Button type="submit" isLoading={isPending}>
                    {isPending ? t("loggingIn") : t("login")}
                </Button>
                <div className="mt-4 flex justify-center gap-1">
                    <p className="text-muted-foreground text-sm">
                        {t("loginForm.dontHaveAccount")}&nbsp;
                        <a href={links.contactUs} className="text-primary hover:underline">
                            {t("loginForm.contactUs")}
                        </a>
                    </p>
                </div>
            </div>
        </Form>
    );
}
