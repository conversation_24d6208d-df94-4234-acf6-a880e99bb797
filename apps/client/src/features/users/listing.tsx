import React from "react";
import { useNavigate } from "@tanstack/react-router";
import { createColumnHelper, type ColumnDef } from "@tanstack/react-table";
import { useTranslation } from "react-i18next";
import type { User } from "@/api/users";
import { DataGrid, useFiltering, useSorting } from "@/components/dataGrid";
import { emptyFallback } from "@/utils/emptyFallback";
import RoleCell from "./roleCell";

const columnHelper = createColumnHelper<User>();

export default function UsersListing({ users }: { users: User[] }) {
    const { t } = useTranslation(["users"]);
    const columns = React.useMemo(
        () =>
            [
                columnHelper.accessor("first_name", {
                    header: t("firstName"),
                    cell: ({ getValue }) => emptyFallback(getValue()),
                    enableSorting: true,
                }),
                columnHelper.accessor("last_name", {
                    header: t("lastName"),
                    cell: ({ getValue }) => emptyFallback(getValue()),
                    enableSorting: true,
                }),
                columnHelper.accessor("email", {
                    header: t("email"),
                    cell: ({ getValue }) => emptyFallback(getValue()),
                    enableSorting: true,
                }),
                columnHelper.accessor("role_id", {
                    header: t("role"),
                    cell: ({ getValue }) => {
                        return <RoleCell roleId={getValue()} />;
                    },
                    enableSorting: true,
                }),
            ] as ColumnDef<User>[],
        [t],
    );

    const sorting = useSorting();
    const columnFilters = useFiltering();
    const navigate = useNavigate();

    return (
        <DataGrid<User>
            columns={columns}
            data={users}
            sorting={sorting}
            columnFilters={columnFilters}
            onRowClick={(row) => navigate({ to: "/users/$id", params: { id: row.id } })}
        />
    );
}
