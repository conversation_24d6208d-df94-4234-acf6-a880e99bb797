import { Link as RouterLink } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";
import { z } from "zod/v4";
import { Form, Text } from "@/components/form";
import { Notification } from "@/components/notification";
import { Button } from "@/components/ui/button";

export type ForgotPasswordFormData = {
    email: string;
};

const schema: z.ZodSchema<ForgotPasswordFormData> = z.object({
    email: z.email(),
});

type Props = {
    onSubmit: (data: ForgotPasswordFormData) => void;
    error?: string;
    isLoading?: boolean;
};

export function ForgotPasswordForm({ onSubmit, error, isLoading }: Props) {
    const { t } = useTranslation();

    return (
        <Form schema={schema} onSubmit={onSubmit} defaultValues={{ email: "" }}>
            <div className="flex flex-col gap-4">
                {error && (
                    <div className="mb-4">
                        <Notification type="error">{error}</Notification>
                    </div>
                )}
                <Text<ForgotPasswordFormData>
                    name="email"
                    label={t("forgotPasswordForm.email")}
                    placeholder={t("forgotPasswordForm.emailPlaceholder")}
                    disabled={isLoading}
                    type="email"
                    autoComplete="email"
                    enablePasswordManagers
                />
                <Button type="submit" isLoading={isLoading}>
                    {t("forgotPasswordForm.sendResetLink")}
                </Button>
                <div className="mt-4 flex justify-center gap-1">
                    <div className="flex justify-end">
                        <RouterLink to="/login" className="text-primary text-sm hover:underline">
                            {t("forgotPasswordForm.backToLogin")}
                        </RouterLink>
                    </div>
                </div>
            </div>
        </Form>
    );
}
