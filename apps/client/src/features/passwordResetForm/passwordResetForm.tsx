import { Link as RouterLink } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";
import { z } from "zod/v4";
import { Form, Password } from "@/components/form";
import { Notification } from "@/components/notification";
import { Button } from "@/components/ui/button";

export type PasswordResetFormData = {
    password: string;
    confirmPassword: string;
};

type Props = {
    onSubmit: (data: PasswordResetFormData) => void;
    error?: string;
    isLoading?: boolean;
};

export function PasswordResetForm({ onSubmit, error, isLoading }: Props) {
    const { t } = useTranslation();

    const schema: z.ZodSchema<PasswordResetFormData> = z
        .object({
            password: z.string().min(6, t("passwordResetForm.validation.passwordMinLength")),
            confirmPassword: z.string().min(6, t("passwordResetForm.validation.passwordMinLength")),
        })
        .refine((data) => data.password === data.confirmPassword, {
            message: t("passwordResetForm.validation.passwordsDoNotMatch"),
            path: ["confirmPassword"],
        });

    return (
        <Form schema={schema} onSubmit={onSubmit} defaultValues={{ password: "", confirmPassword: "" }}>
            <div className="flex flex-col gap-4">
                {error && (
                    <div className="mb-4">
                        <Notification type="error">{error}</Notification>
                    </div>
                )}
                <Password<PasswordResetFormData>
                    name="password"
                    label={t("passwordResetForm.newPassword")}
                    placeholder={t("passwordResetForm.newPasswordPlaceholder")}
                    disabled={isLoading}
                    autoComplete="new-password"
                    enablePasswordManagers
                />
                <Password<PasswordResetFormData>
                    name="confirmPassword"
                    label={t("passwordResetForm.confirmPassword")}
                    placeholder={t("passwordResetForm.confirmPasswordPlaceholder")}
                    disabled={isLoading}
                    autoComplete="new-password"
                    enablePasswordManagers
                />
                <Button type="submit" disabled={isLoading}>
                    {t("passwordResetForm.resetPassword")}
                </Button>
                <div className="mt-4 flex justify-center gap-1">
                    <div className="flex justify-end">
                        <RouterLink to="/login" className="text-primary text-sm hover:underline">
                            {t("passwordResetForm.backToLogin")}
                        </RouterLink>
                    </div>
                </div>
            </div>
        </Form>
    );
}
