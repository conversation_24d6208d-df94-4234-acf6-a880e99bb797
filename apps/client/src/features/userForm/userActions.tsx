import { useSuspenseQuery } from "@tanstack/react-query";
import { Settings } from "lucide-react";
import { useTranslation } from "react-i18next";
import { useUser, useUserPermissions } from "@/api/auth";
import { getUserConfirmationStatusQueryConfig } from "@/api/users/hooks";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { canDeleteUsers, canUpdateUsers } from "@/utils/permissions";
import { DeleteUser } from "./deleteUser";
import { ResetPassword } from "./resetPassword";

type Props = {
    onResendInvitation: () => void;
    onDeleteUser: () => void;
    onResetPassword: () => void;
    userId: string;
};

export function UserActions({ onResendInvitation, onDeleteUser, onResetPassword, userId }: Props) {
    const { t } = useTranslation(["users", "common"]);
    const { data: permissions } = useUserPermissions();
    const { data: user } = useUser();
    const { data: isConfirmed } = useSuspenseQuery(getUserConfirmationStatusQueryConfig(userId));

    const isCurrentUser = user.id === userId;
    const canUpdate = canUpdateUsers(permissions);
    const showDeleteButton = canDeleteUsers(permissions) && !isCurrentUser;
    const showResendInvitationButton = canUpdate && !isCurrentUser && !isConfirmed;

    return (
        <Card>
            <CardHeader>
                <div className="flex items-center gap-2">
                    <Settings className="size-5" />
                    {t("userActionSection")}
                </div>
            </CardHeader>
            <CardContent>
                <div className="flex flex-col gap-4">
                    {showDeleteButton && <DeleteUser onDeleteUser={onDeleteUser} />}
                    {showResendInvitationButton && (
                        <Button variant="secondary" onClick={onResendInvitation}>
                            {t("resendInvitation")}
                        </Button>
                    )}
                    {isCurrentUser && <ResetPassword onResetPassword={onResetPassword} />}
                </div>
            </CardContent>
        </Card>
    );
}
