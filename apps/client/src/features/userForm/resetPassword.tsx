import { useTranslation } from "react-i18next";
import { Confirmation } from "@/components/confirmation";
import { Button } from "@/components/ui/button";

type Props = {
    onResetPassword: () => void;
};

export function ResetPassword({ onResetPassword }: Props) {
    const { t } = useTranslation(["users"]);

    return (
        <Confirmation
            title={t("resetPasswordModal.title")}
            description={t("resetPasswordModal.description")}
            onConfirm={onResetPassword}
            actionButtonVariant="default"
            cancelButtonText={t("resetPasswordModal.cancelButtonText")}
            actionButtonText={t("resetPasswordModal.actionButtonText")}
        >
            <Button variant="secondary">{t("changePassword")}</Button>
        </Confirmation>
    );
}
