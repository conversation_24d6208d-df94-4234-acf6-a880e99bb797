import { useTranslation } from "react-i18next";
import { Confirmation } from "@/components/confirmation";
import { Button } from "@/components/ui/button";

type Props = {
    onDeleteUser: () => void;
};

export function DeleteUser({ onDeleteUser }: Props) {
    const { t } = useTranslation(["users"]);

    return (
        <Confirmation
            title={t("deleteModal.title")}
            description={
                <div>
                    <p className="mb-2 font-medium">{t("deleteModal.description")}</p>
                    <ul className="list-inside list-disc">
                        <li>{t("deleteModal.deletionResult1")}</li>
                        <li>{t("deleteModal.deletionResult2")}</li>
                    </ul>
                    <p className="mb-3">{t("deleteModal.descriptionNotice")}</p>
                    <p className="text-destructive">{t("deleteModal.warning")}</p>
                </div>
            }
            onConfirm={onDeleteUser}
            actionButtonVariant="destructive"
            cancelButtonText={t("deleteModal.cancelButtonText")}
            actionButtonText={t("deleteModal.actionButtonText")}
        >
            <Button variant="destructive">{t("deleteUser")}</Button>
        </Confirmation>
    );
}
