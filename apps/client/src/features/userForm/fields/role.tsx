import { useSuspenseQuery } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { rolesQueryConfig } from "@/api/roles/hooks";
import { Select } from "@/components/form";

export default function Role({ disabled }: { disabled?: boolean }) {
    const { t } = useTranslation(["users"]);
    const { data: roles } = useSuspenseQuery(rolesQueryConfig);

    return (
        <Select
            name="role_id"
            label={t("role")}
            options={roles.map((role) => ({ label: t("role", { context: role.name }), value: role.id }))}
            disabled={disabled}
            placeholder={t("rolePlaceholder")}
        />
    );
}
