import { useTranslation } from "react-i18next";
import { Text } from "@/components/form";
import type { UserForm } from "../types";

type Props = {
    readonly?: boolean;
};

export default function FirstName(props: Props) {
    const { t } = useTranslation(["users"]);

    return (
        <Text<UserForm> name="firstName" label={t("firstName")} placeholder={t("firstNamePlaceholder")} {...props} />
    );
}
