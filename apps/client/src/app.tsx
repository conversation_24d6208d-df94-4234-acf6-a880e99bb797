import { createRouter, RouterProvider } from "@tanstack/react-router";
import { queryClient } from "@/utils/queryClient";
import { routeTree } from "./routeTree.gen";

const router = createRouter({
    routeTree,
    context: {
        queryClient,
    },
    defaultPreload: false,
    scrollRestoration: true,
    defaultStructuralSharing: true,
    defaultPreloadStaleTime: 0,
    defaultStaleTime: 0,
    defaultPreloadGcTime: 0,
    defaultGcTime: 0,
    defaultPendingComponent: () => {
        return <div>Loading...</div>;
    },
});

declare module "@tanstack/react-router" {
    interface Register {
        router: typeof router;
    }
}

export default function App() {
    return <RouterProvider router={router} />;
}
