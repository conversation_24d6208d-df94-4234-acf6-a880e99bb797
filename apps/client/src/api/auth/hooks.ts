import type { AuthError as AuthErrorType, User } from "@supabase/supabase-js";
import type { UseMutationOptions, UseQueryOptions } from "@tanstack/react-query";
import {
    useMutation,
    useQuery,
    useQueryClient,
    useSuspenseQuery,
    type UseSuspenseQueryOptions,
} from "@tanstack/react-query";
import { forgotPassword, getUser, getUserPermissions, signIn, signOut, updateUser } from "./auth";
import type { AuthResponse, Permission, SignInPayload, UpdateUserPayload } from "./types";

export const queryKeys = {
    auth: ["auth"],
    user: () => [...queryKeys.auth, "user"],
    permissions: () => [...queryKeys.user(), "permissions"],
};

export function useSignIn(options?: UseMutationOptions<AuthResponse, AuthErrorType, SignInPayload>) {
    const queryClient = useQueryClient();

    return useMutation({
        ...options,
        mutationFn: signIn,
        onSuccess(data, variables, context) {
            queryClient.setQueryData(queryKeys.auth, data);
            queryClient.invalidateQueries({ queryKey: queryKeys.auth });

            options?.onSuccess?.(data, variables, context);
        },
    });
}

export function useSignOut(options?: UseMutationOptions<unknown, AuthErrorType, void>) {
    const queryClient = useQueryClient();

    return useMutation({
        ...options,
        mutationFn: signOut,
        onSuccess(data, variables, context) {
            queryClient.setQueryData(queryKeys.auth, null);
            queryClient.invalidateQueries({ queryKey: queryKeys.auth });

            options?.onSuccess?.(data, variables, context);
        },
    });
}

export function getUserQueryConfig(): UseQueryOptions<User> {
    return {
        queryKey: queryKeys.auth,
        queryFn: getUser,
        retry: false,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes,
    };
}

export function getUserPermissionsQueryConfig(): UseQueryOptions<Permission[]> {
    return {
        queryKey: queryKeys.permissions(),
        queryFn: getUserPermissions,
        staleTime: Number.POSITIVE_INFINITY,
        gcTime: Number.POSITIVE_INFINITY,
    };
}

export function useGetUser() {
    return useQuery(getUserQueryConfig());
}

export function useUser() {
    return useSuspenseQuery(getUserQueryConfig() as UseSuspenseQueryOptions<User>);
}

/**
 * Uses useSuspenseQuery to fetch user permissions
 * user permissions are fetched at _authenticated route
 */
export function useUserPermissions() {
    return useSuspenseQuery(getUserPermissionsQueryConfig() as UseSuspenseQueryOptions<Permission[]>);
}

export function useForgotPassword(options?: UseMutationOptions<void, Error, string>) {
    return useMutation({
        ...options,
        mutationFn: forgotPassword,
    });
}

export function useUpdateUser(options?: UseMutationOptions<void, Error, UpdateUserPayload>) {
    return useMutation({
        ...options,
        mutationFn: updateUser,
    });
}
