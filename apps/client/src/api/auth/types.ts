import type { AuthTokenResponsePassword } from "@supabase/supabase-js";
import type { permissions } from "./permissions";

export type AuthResponse = {
    session: NonNullable<AuthTokenResponsePassword["data"]["session"]>;
    user: NonNullable<AuthTokenResponsePassword["data"]["user"]>;
    weakPassword?: AuthTokenResponsePassword["data"]["weakPassword"];
};

export type SignInPayload = {
    email: string;
    password: string;
};

export type ForgotPasswordPayload = {
    email: string;
};

export type UpdateUserPayload = {
    email?: string;
    password?: string;
};

export type Permission = {
    [K in keyof typeof permissions]: (typeof permissions)[K][keyof (typeof permissions)[K]];
}[keyof typeof permissions];
