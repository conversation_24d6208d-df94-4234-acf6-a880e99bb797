import type { User } from "@supabase/supabase-js";
import { AuthError } from "@supabase/supabase-js";
import { getSubdomain } from "@/utils/getSubdomain";
import i18n from "@/utils/i18n";
import supabase from "@/utils/supabase";
import type { AuthResponse, Permission, SignInPayload, UpdateUserPayload } from "./types";
import { getAccessToken, removeAccessToken } from "./utils";

export async function forgotPassword(email: string): Promise<void> {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/password-reset`,
    });

    // @TODO:
    // check if user exist in workgroup based on email address

    if (error) {
        return Promise.reject(error);
    }

    return Promise.resolve();
}

export async function updateUser(payload: UpdateUserPayload): Promise<void> {
    const { error } = await supabase.auth.updateUser(payload);

    if (error) {
        if (error.code === "same_password") {
            error.message = i18n.t("common:errors.samePassword");
        }

        return Promise.reject(error);
    }

    return Promise.resolve();
}

export async function signIn(payload: SignInPayload): Promise<AuthResponse> {
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email: payload.email,
        password: payload.password,
    });

    if (signInError) {
        return Promise.reject(signInError);
    }

    const { data: rpcData } = await supabase.rpc("user_exists_in_workgroup", {
        user_id: signInData.user.id,
        subdomain: getSubdomain(),
    });

    if (!rpcData.allow) {
        await supabase.auth.signOut();

        return Promise.reject(new AuthError("Nie możesz zalogować sie do tej aplikacji"));
    }

    return signInData;
}

export function signOut() {
    return supabase.auth.signOut();
}

async function getUserFromCookie(): Promise<User | null> {
    const accessToken = getAccessToken();

    if (!accessToken) {
        return null;
    }

    removeAccessToken();

    const {
        data: { user },
    } = await supabase.auth.getUser(accessToken);

    return Promise.resolve(user);
}

export async function getUser() {
    let user: User | null = null;
    const { data } = await supabase.auth.getUser();

    if (data.user) {
        user = data.user;
    } else {
        user = await getUserFromCookie();
    }

    if (!user) {
        return Promise.reject(new Error("401"));
    }

    const { data: rpcData } = await supabase.rpc("user_exists_in_workgroup", {
        user_id: user.id,
        subdomain: getSubdomain(),
    });

    if (!rpcData.allow) {
        return Promise.reject(new Error("401"));
    }

    return user;
}

export async function getUserPermissions(): Promise<Permission[]> {
    const { data, error } = await supabase.from("role_permissions").select("permission");

    if (error) {
        return Promise.reject(error);
    }

    return data.map((item) => item.permission) as Permission[];
}
