import { queryOptions, useMutation, useQueryClient, type UseMutationOptions } from "@tanstack/react-query";
import type { CreateUserPayload, UpdateUserPayload } from "./types";
import {
    createUser,
    deleteUser,
    getUser,
    getUserConfirmationStatus,
    getUsers,
    resendInvitation,
    resetPassword,
    updateUser,
} from "./users";

export const queryKeys = {
    users: ["users"],
    list: () => [...queryKeys.users, "list"],
    user: (id: string) => [...queryKeys.users, id],
    userConfirmationStatus: (id: string) => [...queryKeys.users, id, "confirmation-status"],
};

export const getUsersQueryConfig = queryOptions({
    queryKey: queryKeys.list(),
    queryFn: getUsers,
});

export const getUserConfirmationStatusQueryConfig = (id: string) =>
    queryOptions({
        queryKey: queryKeys.userConfirmationStatus(id),
        queryFn: () => getUserConfirmationStatus(id),
        staleTime: 5 * 60 * 1000,
    });

export const getUserQueryConfig = (id: string) =>
    queryOptions({
        queryKey: queryKeys.user(id),
        queryFn: () => getUser(id),
    });

export function useUpdateUser(id: string, options?: UseMutationOptions<void, Error, UpdateUserPayload>) {
    const queryClient = useQueryClient();

    return useMutation({
        ...options,
        mutationFn: (user) => updateUser(id, user),
        onSuccess: (data, variables, context) => {
            queryClient.invalidateQueries({ queryKey: queryKeys.list() });
            queryClient.invalidateQueries({ queryKey: queryKeys.user(id) });

            options?.onSuccess?.(data, variables, context);
        },
    });
}

export function useCreateUser(options?: UseMutationOptions<void, Error, CreateUserPayload>) {
    const queryClient = useQueryClient();

    return useMutation({
        ...options,
        mutationFn: (user) => createUser(user),
        onSuccess(data, variables, context) {
            queryClient.invalidateQueries({ queryKey: queryKeys.list() });
            options?.onSuccess?.(data, variables, context);
        },
    });
}

export function useResendInvitation(options?: UseMutationOptions<void, Error, string>) {
    return useMutation({
        ...options,
        mutationFn: (email) => resendInvitation(email),
    });
}

export function useDeleteUser(options?: UseMutationOptions<void, Error, string>) {
    const queryClient = useQueryClient();

    return useMutation({
        ...options,
        mutationFn: (userId) => deleteUser(userId),
        onSuccess(data, variables, context) {
            queryClient.invalidateQueries({ queryKey: queryKeys.list() });
            queryClient.invalidateQueries({ queryKey: queryKeys.user(variables) });
            options?.onSuccess?.(data, variables, context);
        },
    });
}

export function useResetPassword(options?: UseMutationOptions<void, Error, string>) {
    return useMutation({
        ...options,
        mutationFn: (email: string) => resetPassword(email),
    });
}
