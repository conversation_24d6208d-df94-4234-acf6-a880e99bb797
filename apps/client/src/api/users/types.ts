export type User = {
    email: string;
    first_name: string;
    id: string;
    last_name: string;
    role_id: string;
    workgroup_id: string;
};

// TODO: add support for role update and email update
export type UpdateUserPayload = {
    first_name?: string;
    last_name?: string;
    role_id?: string;
};

export type CreateUserPayload = {
    email: string;
    name: string;
    lastName: string;
    roleId: string;
};
