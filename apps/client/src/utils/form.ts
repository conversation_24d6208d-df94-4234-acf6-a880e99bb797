import { useRef } from "react";

export function useFormSubmit(
    existingRef?: React.RefObject<HTMLElement | null>,
): [React.RefObject<HTMLElement | null>, () => void] {
    const internalRef = useRef<HTMLElement>(null);
    const ref = existingRef || internalRef;

    const submitForm = () => {
        if (!ref.current) return;

        const form = ref.current.closest("form");
        if (form) {
            form.requestSubmit();
        }
    };

    return [ref, submitForm];
}
