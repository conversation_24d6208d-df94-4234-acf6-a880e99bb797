import type { Permission } from "@/api/auth";
import { permissions } from "@/api/auth/permissions";

export function canListUsers(userPermissions: Permission[]) {
    return userPermissions.includes(permissions.users.list);
}

export function canViewUserProfile(userPermissions: Permission[], userId: string, currentUserId: string) {
    return userPermissions.includes(permissions.users.list) || userId === currentUserId;
}

export function canCreateUsers(userPermissions: Permission[]) {
    return userPermissions.includes(permissions.users.create);
}

export function canDeleteUsers(userPermissions: Permission[]) {
    return userPermissions.includes(permissions.users.delete);
}

export function canUpdateUsers(userPermissions: Permission[]) {
    return userPermissions.includes(permissions.users.update);
}
