import { type Breadcrumb } from "@/components/breadcrumbs/breadcrumbs";

export function createBreadcrumbConfig(items: Omit<Breadcrumb, "isActive">[]): Breadcrumb[] {
    const breadcrumbs = items.map((item) => ({ ...item, isActive: false }));
    breadcrumbs[breadcrumbs.length - 1].isActive = true;

    return breadcrumbs;
}

export function createBreadcrumbItem(label: string, to: Breadcrumb["to"]): Breadcrumb {
    return { label, to, isActive: false };
}
