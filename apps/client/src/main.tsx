import { StrictMode } from "react";
import { QueryClientProvider } from "@tanstack/react-query";
import { createRoot } from "react-dom/client";
import BizzuToaster from "@/components/toaster";
import { queryClient } from "@/utils/queryClient";
import App from "./app";
import "./index.css";
import "./utils/i18n";

createRoot(document.getElementById("root")!).render(
    <StrictMode>
        <QueryClientProvider client={queryClient}>
            <App />
            <BizzuToaster />
        </QueryClientProvider>
    </StrictMode>,
);
