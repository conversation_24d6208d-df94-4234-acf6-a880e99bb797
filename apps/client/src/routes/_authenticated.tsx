import { createFileRoute, Outlet, redirect, useRouterState } from "@tanstack/react-router";
import { getUserPermissionsQueryConfig, getUserQueryConfig } from "@/api/auth";
import { AppLayout } from "@/components/layouts/appLayout";

function AuthenticatedLayout() {
    const location = useRouterState({ select: (s) => s.location.pathname });
    const showSidebar = location !== "/password-reset";

    return (
        <AppLayout showSidebar={showSidebar}>
            <Outlet />
        </AppLayout>
    );
}

export const Route = createFileRoute("/_authenticated")({
    beforeLoad: async ({ context, location }) => {
        const { queryClient } = context;
        try {
            await queryClient.fetchQuery(getUserQueryConfig());
            await queryClient.fetchQuery(getUserPermissionsQueryConfig());
        } catch (e) {
            if (e instanceof Error && e.message.includes("401")) {
                throw redirect({ to: "/login", search: { redirect: location.pathname } });
            }
        }
    },
    component: AuthenticatedLayout,
});
