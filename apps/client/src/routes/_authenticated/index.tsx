import { createFileRoute } from "@tanstack/react-router";
import { Breadcrumbs } from "@/components/breadcrumbs/breadcrumbs";
import { ModuleLayout } from "@/components/layouts/moduleLayout";
import { createBreadcrumbConfig, createBreadcrumbItem } from "@/utils/breadcrumbs";

export const Route = createFileRoute("/_authenticated/")({
    component: RouteComponent,
});

function RouteComponent() {
    const breadcrumbs = createBreadcrumbConfig([createBreadcrumbItem("Home", "/")]);

    return (
        <ModuleLayout>
            <ModuleLayout.Header>
                <Breadcrumbs breadcrumbs={breadcrumbs} />
            </ModuleLayout.Header>
            <ModuleLayout.Content>
                <p>To be continued...</p>
            </ModuleLayout.Content>
        </ModuleLayout>
    );
}
