import { createFileRoute, Link, redirect, useLoaderData } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";
import type { Permission } from "@/api/auth";
import { getUserPermissionsQueryConfig } from "@/api/auth/hooks";
import { getUsers } from "@/api/users";
import { Breadcrumbs } from "@/components/breadcrumbs/breadcrumbs";
import { ModuleLayout } from "@/components/layouts/moduleLayout";
import { Button } from "@/components/ui/button";
import UsersListing from "@/features/users/listing";
import { createBreadcrumbConfig, createBreadcrumbItem } from "@/utils/breadcrumbs";
import { canListUsers } from "@/utils/permissions";

export const Route = createFileRoute("/_authenticated/users/")({
    component: RouteComponent,
    loader: async ({ context }) => {
        const { queryClient } = context;
        const permissions = queryClient.getQueryData<Permission[]>(getUserPermissionsQueryConfig().queryKey) ?? [];

        if (!canListUsers(permissions)) {
            throw redirect({ to: "/" });
        }

        const users = await getUsers();

        return { users };
    },
});

function RouteComponent() {
    const breadcrumbs = createBreadcrumbConfig([createBreadcrumbItem("Pracownicy", "/users")]);
    const { t } = useTranslation(["users"]);
    const { users } = useLoaderData({ from: "/_authenticated/users/" });

    return (
        <ModuleLayout>
            <ModuleLayout.Header>
                <div className="flex flex-col gap-2">
                    <Breadcrumbs breadcrumbs={breadcrumbs} />
                    <h2 className="text-muted-foreground text-sm">{t("moduleSubtitle")}</h2>
                </div>
                <Link to="/users/new">
                    <Button size="sm" variant="default">
                        {t("addWorker")}
                    </Button>
                </Link>
            </ModuleLayout.Header>
            <ModuleLayout.Content>
                <UsersListing users={users} />
            </ModuleLayout.Content>
        </ModuleLayout>
    );
}
