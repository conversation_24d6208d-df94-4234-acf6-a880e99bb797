import { createFileRoute, redirect, useRouter } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import type { Permission } from "@/api/auth";
import { getUserPermissionsQueryConfig } from "@/api/auth/hooks";
import { useCreateUser } from "@/api/users/hooks";
import { Breadcrumbs } from "@/components/breadcrumbs/breadcrumbs";
import { ModuleLayout } from "@/components/layouts/moduleLayout";
import { UserForm } from "@/features/userForm";
import type { UserForm as UserFormType } from "@/features/userForm/types";
import { createBreadcrumbConfig, createBreadcrumbItem } from "@/utils/breadcrumbs";
import { canCreateUsers } from "@/utils/permissions";

export const Route = createFileRoute("/_authenticated/users/new")({
    component: RouteComponent,
    loader: async ({ context }) => {
        const { queryClient } = context;
        const permissions = queryClient.getQueryData<Permission[]>(getUserPermissionsQueryConfig().queryKey) ?? [];

        if (!canCreateUsers(permissions)) {
            throw redirect({ to: "/users" });
        }

        return { permissions };
    },
});

function RouteComponent() {
    const breadcrumbs = createBreadcrumbConfig([
        createBreadcrumbItem("Pracownicy", "/users"),
        createBreadcrumbItem("Dodaj pracownika", "/users/new"),
    ]);
    const { t } = useTranslation(["users"]);
    const router = useRouter();
    const { mutate: createUser } = useCreateUser();

    const handleSubmit = (data: UserFormType) => {
        createUser(
            {
                email: data.email,
                name: data.firstName,
                lastName: data.lastName,
                roleId: data.role_id,
            },
            {
                onSuccess: () => {
                    toast.success(t("userCreated"));
                    router.navigate({ to: "/users" });
                },
                onError: () => {
                    toast.error(t("userCreateFailed"));
                },
            },
        );
    };

    return (
        <ModuleLayout>
            <ModuleLayout.Header>
                <Breadcrumbs breadcrumbs={breadcrumbs} />
            </ModuleLayout.Header>
            <ModuleLayout.Content>
                <div className="grid gap-4 lg:grid-cols-3">
                    <UserForm user={undefined} onSubmit={handleSubmit} />
                </div>
            </ModuleLayout.Content>
        </ModuleLayout>
    );
}
