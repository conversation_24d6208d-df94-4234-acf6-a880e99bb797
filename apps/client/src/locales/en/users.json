{"addWorker": "Add worker", "moduleSubtitle": "Add new, edit existing by clicking on their details.", "firstName": "First name", "firstNamePlaceholder": "Worker's first name", "lastName": "Last name", "lastNamePlaceholder": "Worker's last name", "email": "Email", "emailPlaceholder": "Worker's email", "role": "Role", "rolePlaceholder": "Select role…", "role_admin": "Admin", "primaryDataSection": "Worker's data", "userActionSection": "Actions", "deleteUser": "Delete user", "resendInvitation": "Resend invitation", "changePassword": "Change password", "userUpdated": "User updated", "userUpdateFailed": "User update failed", "userCreated": "User created", "userCreateFailed": "User create failed", "invitationSent": "Invitation sent", "invitationSentFailed": "Invitation send failed", "deletedUser": "User deleted", "deletedUserFailed": "User delete failed", "deleteModal": {"title": "Delete user", "description": "Deleting a user will result in:", "deletionResult1": "removing their ability to log in to the system", "deletionResult2": "removing them from the list of users", "descriptionNotice": "However, their data will still be visible in places where they are mentioned.", "warning": "This operation is irreversible.", "actionButtonText": "Delete", "cancelButtonText": "Cancel"}, "resetPasswordModal": {"title": "Change password", "description": "To change your password, we will send you a link, which you will need to click to set a new password.", "actionButtonText": "Send link to change password", "cancelButtonText": "Cancel"}, "passwordResetPage": {"title": "Check your email", "description": "We've successfully sent you a password reset link. Click the link in your email to set a new password.", "backToLogin": "Back to login"}}