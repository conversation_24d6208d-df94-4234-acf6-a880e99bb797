name: Push Schema to Production

on:
  push:
    branches: [main]
    paths:
      - "supabase/**"

jobs:
  push-schema-prod:
    runs-on: ubuntu-latest
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "22"
          cache: "npm"
          cache-dependency-path: "supabase/package-lock.json"

      - name: Setup Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: latest

      - name: Push Supabase database schema
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN_PROD }}
          SUPABASE_DB_PASSWORD: ${{ secrets.SUPABASE_DB_PASSWORD_PROD }}
          SUPABASE_PROJECT_ID: ${{ secrets.SUPABASE_PROJECT_ID_PROD }}
        run: |
          supabase link --project-ref "$SUPABASE_PROJECT_ID"
          supabase db push
          supabase functions deploy --project-ref "$SUPABASE_PROJECT_ID"
