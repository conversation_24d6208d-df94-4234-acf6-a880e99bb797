name: PR Checks for <PERSON>hem<PERSON>

on:
  pull_request:
    branches: [develop]
    types: [opened, synchronize, reopened]
    paths:
      - "supabase/**"

jobs:
  push-schema-dry-run:
    runs-on: ubuntu-latest
    environment: development

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "22"
          cache: "npm"
          cache-dependency-path: "supabase/package-lock.json"

      - name: Install Supabase dependencies
        working-directory: ./supabase
        run: npm ci

      - name: Setup Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: latest

      - name: Start local Supabase
        working-directory: ./supabase
        run: |
          supabase start

      - name: Reset database to apply all migrations
        working-directory: ./supabase
        run: |
          supabase db reset --local

      - name: Lint database schema
        working-directory: ./supabase
        run: |
          supabase db lint --local --fail-on error
