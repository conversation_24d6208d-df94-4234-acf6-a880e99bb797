name: Push Schema to Development

on:
  push:
    branches: [develop]
    paths:
      - "supabase/**"

jobs:
  push-schema-dev:
    runs-on: ubuntu-latest
    environment: development

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "22"
          cache: "npm"
          cache-dependency-path: "supabase/package-lock.json"

      - name: Setup Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: latest

      - name: Push Supabase database schema
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN_DEV }}
          SUPABASE_DB_PASSWORD: ${{ secrets.SUPABASE_DB_PASSWORD_DEV }}
          SUPABASE_PROJECT_ID: ${{ secrets.SUPABASE_PROJECT_ID_DEV }}
        run: |
          supabase link --project-ref "$SUPABASE_PROJECT_ID"
          supabase db push
          supabase functions deploy --project-ref "$SUPABASE_PROJECT_ID"
