import { <PERSON>o } from "@hono/hono";
import type { User } from "@supabase/supabase-js";
import { zValidator } from "npm:@hono/zod-validator";
import { bearerAuth } from "npm:hono@^4.0.0/bearer-auth";
import { cors } from "npm:hono@^4.0.0/cors";
import { z } from "npm:zod";
import { hasPermission, permissions } from "../_shared/permissions.ts";
import { supabaseAdmin } from "../_shared/supabaseAdmin.ts";
import { createUser, deleteUser, resendInvitation } from "./utils.ts";

const functionName = "users";
const app = new Hono().basePath(`/${functionName}`);

declare module "@hono/hono" {
  interface ContextVariableMap {
    user: User;
  }
}

app.use(
  "*",
  cors({
    origin: (origin) => {
      if (!origin) return null;
      try {
        const url = new URL(origin);
        const host = url.hostname.toLowerCase();
        if (
          host === "localhost" ||
          host.endsWith(".bizzu.app") ||
          host.endsWith(".bizzu.dev")
        ) {
          return origin;
        }
      } catch (_) {
        // ignore invalid Origin header values
      }
      return null;
    },
  })
);

app.use(
  "*",
  bearerAuth({
    verifyToken: async (token, c) => {
      const { data, error } = await supabaseAdmin.auth.getUser(token);

      if (error || !data) {
        return false;
      }

      c.set("user", data.user);

      return true;
    },
  })
);

app.post(
  "/create",
  zValidator(
    "json",
    z.object({
      email: z.email(),
      name: z.string(),
      lastName: z.string(),
      roleId: z.string(),
    })
  ),
  async (c) => {
    const authUser = c.get("user");
    const { email, name, lastName, roleId } = c.req.valid("json");

    const canCreateUsers = await hasPermission(
      permissions.users.create,
      authUser.id
    );

    if (!canCreateUsers) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    const { data, error } = await createUser({
      email,
      name,
      lastName,
      roleId,
      workgroupId: authUser.app_metadata.workgroup_id,
    });

    if (error) {
      return c.json({ error: error.message }, 500);
    }

    return c.json({ id: data.user.id });
  }
);

app.post(
  "/resend-invitation",
  zValidator(
    "json",
    z.object({
      email: z.email(),
    })
  ),
  async (c) => {
    const authUser = c.get("user");
    const { email } = c.req.valid("json");

    const canCreateUsers = await hasPermission(
      permissions.users.create,
      authUser.id
    );

    if (!canCreateUsers) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    const { error } = await resendInvitation(email);

    if (error) {
      return c.json({ error: error.message }, 500);
    }

    return c.json({ ok: true });
  }
);

app.delete("/:id", async (c) => {
  const authUser = c.get("user");
  const { id } = c.req.param();

  const canDeleteUsers = await hasPermission(
    permissions.users.delete,
    authUser.id
  );

  if (!canDeleteUsers) {
    return c.json({ error: "Unauthorized" }, 401);
  }

  const [{ error: postgresError }, { error: authError }] = await deleteUser(id);

  if (postgresError || authError) {
    return c.json({ error: postgresError?.message || authError?.message }, 500);
  }

  return c.json({ ok: true });
});

Deno.serve(app.fetch);
