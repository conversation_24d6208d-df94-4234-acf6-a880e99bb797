import { supabaseAdmin } from "../_shared/supabaseAdmin.ts";

type CreateUserParams = {
  email: string;
  name: string;
  lastName: string;
  roleId: string;
  workgroupId: string;
};

export async function createUser({
  email,
  name,
  lastName,
  roleId,
  workgroupId,
}: CreateUserParams) {
  return await supabaseAdmin.auth.admin.inviteUserByEmail(email, {
    data: {
      userCreation: {
        workgroup: workgroupId,
        roleId,
        first_name: name,
        last_name: lastName,
      },
    },
  });
}

export async function resendInvitation(email: string) {
  return await supabaseAdmin.auth.resend({
    type: "signup",
    email,
  });
}

export async function deleteUser(userId: string) {
  const shouldSoftDelete = true;
  return await Promise.all([
    supabaseAdmin
      .from("user_profiles")
      .update({
        deleted_at: new Date().toISOString(),
      })
      .eq("id", userId),
    supabaseAdmin.auth.admin.deleteUser(userId, shouldSoftDelete),
  ]);
}
