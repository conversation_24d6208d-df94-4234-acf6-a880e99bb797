import { supabaseAdmin } from "./supabaseAdmin.ts";

export const permissions = {
  users: {
    list: "users.list",
    create: "users.create",
    delete: "users.delete",
    update: "users.update",
  },
};

export const hasPermission = async (permission: string, user_id: string) => {
  const { data, error } = await supabaseAdmin
    .from("user_profiles")
    .select("role_id")
    .eq("id", user_id)
    .single();

  if (error || !data) {
    return false;
  }

  const { data: rolePermission, error: rolePermissionsError } =
    await supabaseAdmin
      .from("role_permissions")
      .select("permission")
      .eq("role_id", data.role_id)
      .eq("permission", permission)
      .single();

  if (rolePermissionsError || !rolePermission) {
    return false;
  }

  return true;
};
