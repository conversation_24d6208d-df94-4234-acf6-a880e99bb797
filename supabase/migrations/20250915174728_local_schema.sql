set check_function_bodies = off;

CREATE OR R<PERSON>LACE FUNCTION public.has_permission(requested_permission permission, row_workgroup_id uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 STABLE SECURITY DEFINER
 SET search_path TO ''
AS $function$
declare
  user_role UUID;
  user_workgroup_id uuid;
begin
  -- Extract workgroup_id from JWT metadata
  user_workgroup_id := (auth.jwt() -> 'app_metadata' ->> 'workgroup_id')::uuid;

  if user_workgroup_id is null or user_workgroup_id != row_workgroup_id then
    return false;
  end if;

  select role_id into user_role from public.user_profiles where id = (auth.uid())::uuid;

  RETURN EXISTS (
    SELECT 1
    FROM public.role_permissions rp
    WHERE rp.permission = requested_permission
      AND rp.role_id = user_role
  );
end;
$function$
;

create policy "Allow authorized list users"
on "public"."user_profiles"
as permissive
for select
to authenticated
using (( SELECT has_permission('users.list'::permission, user_profiles.workgroup_id) AS has_permission));



