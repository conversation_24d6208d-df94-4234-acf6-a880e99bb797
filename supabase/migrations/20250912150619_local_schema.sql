create type "public"."permission" as enum ('users.list', 'users.create', 'users.delete', 'users.update');

drop policy "Enable read access for authenticated" on "public"."clients";

drop policy "clients_select" on "public"."clients";

drop policy "Allow All for dashboard users, admins and postgres" on "public"."workgroups";

revoke delete on table "public"."user_profiles" from "anon";

revoke insert on table "public"."user_profiles" from "anon";

revoke references on table "public"."user_profiles" from "anon";

revoke select on table "public"."user_profiles" from "anon";

revoke trigger on table "public"."user_profiles" from "anon";

revoke truncate on table "public"."user_profiles" from "anon";

revoke update on table "public"."user_profiles" from "anon";

revoke delete on table "public"."user_profiles" from "authenticated";

revoke insert on table "public"."user_profiles" from "authenticated";

revoke references on table "public"."user_profiles" from "authenticated";

revoke trigger on table "public"."user_profiles" from "authenticated";

revoke truncate on table "public"."user_profiles" from "authenticated";

revoke update on table "public"."user_profiles" from "authenticated";

drop function if exists "public"."create_user_profile"(user_id uuid, workgroup_id uuid, role text);

drop function if exists "public"."register_workgroup_admin"(invitation_code text, subdomain text, company_name text, user_id uuid);

create table "public"."role_permissions" (
    "permission" permission not null,
    "role_id" uuid not null,
    "id" uuid not null default gen_random_uuid()
);


alter table "public"."role_permissions" enable row level security;

create table "public"."roles" (
    "id" uuid not null default gen_random_uuid(),
    "name" text not null,
    "workgroup_id" uuid not null,
    "is_system_role" boolean not null default false
);


alter table "public"."roles" enable row level security;

alter table "public"."user_profiles" drop column "role";

alter table "public"."user_profiles" add column "email" text;

alter table "public"."user_profiles" add column "first_name" text;

alter table "public"."user_profiles" add column "last_name" text;

alter table "public"."user_profiles" add column "role_id" uuid;

CREATE UNIQUE INDEX role_permissions_pkey ON public.role_permissions USING btree (id);

CREATE UNIQUE INDEX roles_pkey ON public.roles USING btree (id);

alter table "public"."role_permissions" add constraint "role_permissions_pkey" PRIMARY KEY using index "role_permissions_pkey";

alter table "public"."roles" add constraint "roles_pkey" PRIMARY KEY using index "roles_pkey";

alter table "public"."role_permissions" add constraint "role_permissions_role_id_fkey" FOREIGN KEY (role_id) REFERENCES roles(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."role_permissions" validate constraint "role_permissions_role_id_fkey";

alter table "public"."roles" add constraint "roles_workgroup_id_fkey" FOREIGN KEY (workgroup_id) REFERENCES workgroups(id) ON UPDATE CASCADE not valid;

alter table "public"."roles" validate constraint "roles_workgroup_id_fkey";

alter table "public"."user_profiles" add constraint "user_profiles_role_id_fkey" FOREIGN KEY (role_id) REFERENCES roles(id) ON UPDATE CASCADE not valid;

alter table "public"."user_profiles" validate constraint "user_profiles_role_id_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.create_default_admin_role(workgroup_id uuid)
 RETURNS json
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  role_id UUID;
  permission_type public.permission;
BEGIN
  INSERT INTO public.roles (name, workgroup_id, is_system_role)
  VALUES ('admin', workgroup_id, true)
  RETURNING id INTO role_id;

  FOR permission_type IN SELECT unnest(enum_range(NULL::public.permission)) LOOP
    INSERT INTO public.role_permissions(permission, role_id)
    VALUES (permission_type, role_id);
  END LOOP;

  RETURN json_build_object(
    'success', true,
    'role_id', role_id
  );

EXCEPTION
WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', SQLERRM
    );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.create_user_profile(user_id uuid, workgroup_id uuid, role_id uuid, first_name text, last_name text, email text)
 RETURNS json
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  INSERT INTO public.user_profiles (id, workgroup_id, role_id, first_name, last_name, email)
  VALUES (user_id, workgroup_id, role_id, first_name, last_name, email);
  
  RETURN json_build_object('success', true);
EXCEPTION
  WHEN unique_violation THEN
    RETURN json_build_object(
      'success', false,
      'error', 'User profile already exists'
    );
  WHEN foreign_key_violation THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Invalid user or workgroup ID'
    );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.setup_new_domain(invitation_code text, subdomain text, company_name text, email text, user_id uuid)
 RETURNS json
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  client_result JSON;
  workgroup_result JSON;
  profile_result JSON;
  mark_result JSON;
  client_id UUID;
  workgroup_id UUID;
  admin_role JSON;
  role_id UUID;
BEGIN
  -- Start explicit transaction
  BEGIN  
    SELECT public.find_or_create_client(company_name) INTO client_result;
    
    IF NOT (client_result->>'success')::boolean THEN
      RAISE EXCEPTION 'Client creation failed %', client_result->>'error';
    END IF;
    
    client_id := (client_result->>'client_id')::uuid;
    
    SELECT public.create_workgroup(subdomain, client_id) INTO workgroup_result;
    
    IF NOT (workgroup_result->>'success')::boolean THEN
      RAISE EXCEPTION 'Workgroup creation failed %', workgroup_result->>'error';
    END IF;
    
    workgroup_id := (workgroup_result->>'workgroup_id')::uuid;

    SELECT public.create_default_admin_role(workgroup_id) INTO admin_role;

    IF NOT (admin_role->>'success')::boolean THEN
      RAISE EXCEPTION 'Default admin role creation failed %', admin_role->>'error';
    END IF;

    role_id := (admin_role->>'role_id')::uuid;

    SELECT public.create_user_profile(user_id, workgroup_id, role_id, '', '', email) INTO profile_result;
    
    IF NOT (profile_result->>'success')::boolean THEN
      RAISE EXCEPTION 'User profile creation failed %', profile_result->>'error';
    END IF;
    
    SELECT public.mark_invitation_used(invitation_code::UUID, user_id) INTO mark_result;
    
    IF NOT (mark_result->>'success')::boolean THEN
      RAISE EXCEPTION 'Invitation code activation failed %', mark_result->>'error';
    END IF;
    
    RAISE NOTICE 'Successfully registered workgroup admin for user %', user_id;

    RETURN json_build_object(
        'success', true,
        'message', 'Successfully registered workgroup admin',
        'workgroup_id', workgroup_id,
        'client_id', client_id,
        'user_id', user_id
      );
  EXCEPTION 
    WHEN OTHERS THEN
      RAISE LOG 'Registration error for user %: %', user_id, SQLERRM;
      RAISE;
  END;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_user_app_metadata_workgroup(user_id uuid, workgroup_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
  UPDATE auth.users 
  SET raw_app_meta_data = COALESCE(raw_app_meta_data, '{}'::jsonb) || 
                          jsonb_build_object('workgroup_id', workgroup_id)
  WHERE id = user_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.after_user_insert()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    user_metadata JSONB;
    workgroup_id UUID;
BEGIN
    -- Start explicit transaction
    BEGIN
        user_metadata := COALESCE(NEW.raw_user_meta_data, '{}'::jsonb);

        IF user_metadata ? 'domainCreation' THEN
            BEGIN
                -- Create workgroup and get the workgroup_id directly
                SELECT (public.setup_new_domain(
                    user_metadata->'domainCreation'->>'invitationCode'::TEXT,
                    user_metadata->'domainCreation'->>'subdomain'::TEXT,
                    user_metadata->'domainCreation'->>'companyName'::TEXT,
                    NEW.email,
                    NEW.id
                )->>'workgroup_id')::uuid INTO workgroup_id;
                
                PERFORM public.update_user_app_metadata_workgroup(NEW.id, workgroup_id);
                
            EXCEPTION
                WHEN OTHERS THEN
                    DELETE FROM auth.users WHERE id = NEW.id;
                    RAISE;
            END;
        ELSIF user_metadata ? 'userCreation' THEN
            BEGIN
                SELECT id INTO workgroup_id
                FROM public.workgroups
                WHERE subdomain = user_metadata->'userCreation'->>'subdomain';  

                PERFORM public.create_user_profile(
                    NEW.id,
                    workgroup_id,
                    user_metadata->'userCreation'->>'role'::uuid
                );
                
                PERFORM public.update_user_app_metadata_workgroup(NEW.id, workgroup_id);
                
            EXCEPTION
                WHEN OTHERS THEN
                    DELETE FROM auth.users WHERE id = NEW.id;
                    RAISE;
            END;
        ELSE 
            DELETE FROM auth.users WHERE id = NEW.id;
            RAISE EXCEPTION 'Invalid user metadata';
        END IF;

        RETURN NEW;
    EXCEPTION
        WHEN OTHERS THEN
            DELETE FROM auth.users WHERE id = NEW.id;
            RAISE;
    END;
END;
$function$
;

grant delete on table "public"."role_permissions" to "anon";

grant insert on table "public"."role_permissions" to "anon";

grant references on table "public"."role_permissions" to "anon";

grant select on table "public"."role_permissions" to "anon";

grant trigger on table "public"."role_permissions" to "anon";

grant truncate on table "public"."role_permissions" to "anon";

grant update on table "public"."role_permissions" to "anon";

grant delete on table "public"."role_permissions" to "authenticated";

grant insert on table "public"."role_permissions" to "authenticated";

grant references on table "public"."role_permissions" to "authenticated";

grant select on table "public"."role_permissions" to "authenticated";

grant trigger on table "public"."role_permissions" to "authenticated";

grant truncate on table "public"."role_permissions" to "authenticated";

grant update on table "public"."role_permissions" to "authenticated";

grant delete on table "public"."role_permissions" to "service_role";

grant insert on table "public"."role_permissions" to "service_role";

grant references on table "public"."role_permissions" to "service_role";

grant select on table "public"."role_permissions" to "service_role";

grant trigger on table "public"."role_permissions" to "service_role";

grant truncate on table "public"."role_permissions" to "service_role";

grant update on table "public"."role_permissions" to "service_role";

grant delete on table "public"."roles" to "anon";

grant insert on table "public"."roles" to "anon";

grant references on table "public"."roles" to "anon";

grant select on table "public"."roles" to "anon";

grant trigger on table "public"."roles" to "anon";

grant truncate on table "public"."roles" to "anon";

grant update on table "public"."roles" to "anon";

grant delete on table "public"."roles" to "authenticated";

grant insert on table "public"."roles" to "authenticated";

grant references on table "public"."roles" to "authenticated";

grant select on table "public"."roles" to "authenticated";

grant trigger on table "public"."roles" to "authenticated";

grant truncate on table "public"."roles" to "authenticated";

grant update on table "public"."roles" to "authenticated";

grant delete on table "public"."roles" to "service_role";

grant insert on table "public"."roles" to "service_role";

grant references on table "public"."roles" to "service_role";

grant select on table "public"."roles" to "service_role";

grant trigger on table "public"."roles" to "service_role";

grant truncate on table "public"."roles" to "service_role";

grant update on table "public"."roles" to "service_role";

grant delete on table "public"."user_profiles" to "supabase_auth_admin";

grant insert on table "public"."user_profiles" to "supabase_auth_admin";

grant references on table "public"."user_profiles" to "supabase_auth_admin";

grant select on table "public"."user_profiles" to "supabase_auth_admin";

grant trigger on table "public"."user_profiles" to "supabase_auth_admin";

grant truncate on table "public"."user_profiles" to "supabase_auth_admin";

grant update on table "public"."user_profiles" to "supabase_auth_admin";

