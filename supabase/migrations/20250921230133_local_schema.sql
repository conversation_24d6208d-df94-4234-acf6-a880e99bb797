set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.after_user_insert()
 <PERSON><PERSON><PERSON><PERSON> trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    user_metadata JSONB;
    workgroup_id UUID;
BEGIN
    -- Start explicit transaction
    BEGIN
        user_metadata := COALESCE(NEW.raw_user_meta_data, '{}'::jsonb);

        IF user_metadata ? 'domainCreation' THEN
            BEGIN
                -- Create workgroup and get the workgroup_id directly
                SELECT (public.setup_new_domain(
                    user_metadata->'domainCreation'->>'invitationCode'::TEXT,
                    user_metadata->'domainCreation'->>'subdomain'::TEXT,
                    user_metadata->'domainCreation'->>'companyName'::TEXT,
                    NEW.email,
                    NEW.id
                )->>'workgroup_id')::uuid INTO workgroup_id;
                
                PERFORM public.update_user_app_metadata_workgroup(NEW.id, workgroup_id);
                
            EXCEPTION
                WHEN OTHERS THEN
                    DELETE FROM auth.users WHERE id = NEW.id;
                    RAISE;
            END;
        ELSIF user_metadata ? 'userCreation' THEN
            BEGIN
                -- Extract and validate workgroup UUID
                workgroup_id := (user_metadata->'userCreation'->>'workgroup')::uuid;
                
                -- Validate that workgroup_id is not null
                IF workgroup_id IS NULL THEN
                    RAISE EXCEPTION 'workgroup_id cannot be null in userCreation metadata';
                END IF;

                PERFORM public.create_user_profile(
                    NEW.id,
                    workgroup_id,
                    (user_metadata->'userCreation'->>'roleId')::uuid,
                    user_metadata->'userCreation'->>'first_name'::TEXT,
                    user_metadata->'userCreation'->>'last_name'::TEXT,
                    NEW.email
                );
                
                PERFORM public.update_user_app_metadata_workgroup(NEW.id, workgroup_id);
                
            EXCEPTION
                WHEN OTHERS THEN
                    DELETE FROM auth.users WHERE id = NEW.id;
                    RAISE;
            END;
        ELSE 
            DELETE FROM auth.users WHERE id = NEW.id;
            RAISE EXCEPTION 'Invalid user metadata';
        END IF;

        RETURN NEW;
    EXCEPTION
        WHEN OTHERS THEN
            DELETE FROM auth.users WHERE id = NEW.id;
            RAISE;
    END;
END;
$function$
;


