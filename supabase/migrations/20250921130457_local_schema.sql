set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.can_read_permission(role_id_param uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 STABLE SECURITY DEFINER
 SET search_path TO ''
AS $function$
-- declare
--   role_id_var UUID;
BEGIN
  -- select role_id into role_id_var from public.user_profiles where id = (auth.uid())::uuid;

  -- RAISE LOG 'ROLE_ID_VAR: %', role_id_var;
  -- RAISE LOG 'ROW_ID: %', row_id; 

  RETURN EXISTS (
    SELECT 1
    FROM public.user_profiles up
    WHERE up.id = (auth.uid())::uuid
      AND up.role_id = role_id_param
  );
END;
$function$
;

create policy "Allow authorized list roles"
on "public"."roles"
as permissive
for select
to authenticated
using (( SELECT has_permission('users.update'::permission, roles.workgroup_id) AS has_permission));



