set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.can_read_permission(role_id_param uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 STABLE SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM public.user_profiles up
    WHERE up.id = (auth.uid())::uuid
      AND up.role_id = role_id_param
  );
END;
$function$
;

create policy "User can read his permissions"
on "public"."role_permissions"
as permissive
for select
to authenticated
using (( SELECT can_read_permission(role_permissions.role_id) AS can_read_permission));



