{"name": "supabase", "version": "1.0.0", "scripts": {"db:start": "supabase start", "functions:start": "supabase functions serve", "db:stop": "supabase stop", "db:diff": "supabase db diff --local --schema public --file local_schema", "db:push": "supabase db push --local", "db:reset": "supabase db reset --local"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@postgrestools/postgrestools": "^0.13.0", "supabase": "^2.40.7"}}