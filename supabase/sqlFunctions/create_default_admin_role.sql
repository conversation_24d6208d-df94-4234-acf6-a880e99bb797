CREATE OR REPLACE FUNCTION create_default_admin_role(workgroup_id UUID) RETURNS JSON AS $$
DECLARE
  role_id UUID;
  permission_type public.permission;
BEGIN
  INSERT INTO public.roles (name, workgroup_id, is_system_role)
  VALUES ('admin', workgroup_id, true)
  RETURNING id INTO role_id;

  FOR permission_type IN SELECT unnest(enum_range(NULL::public.permission)) LOOP
    INSERT INTO public.role_permissions(permission, role_id)
    VALUES (permission_type, role_id);
  END LOOP;

  RETURN json_build_object(
    'success', true,
    'role_id', role_id
  );

EXCEPTION
WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
