CREATE OR REPLACE FUNCTION create_workgroup(
  subdomain TEXT,
  client_id UUID
) RETURNS JSON AS $$
DECLARE
  new_workgroup_id UUID;
BEGIN
  INSERT INTO public.workgroups (id, subdomain, client_id)
  VALUES (gen_random_uuid(), subdomain, client_id)
  RETURNING id INTO new_workgroup_id;
  
  RETURN json_build_object(
    'success', true,
    'workgroup_id', new_workgroup_id
  );

EXCEPTION
  WHEN unique_violation THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Subdomain already taken'
    );
  WHEN foreign_key_violation THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Invalid client ID'
    );
END;

$$ LANGUAGE plpgsql SECURITY DEFINER;
