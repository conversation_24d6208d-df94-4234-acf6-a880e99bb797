create or replace function public.has_permission(
  requested_permission public.permission,
  row_workgroup_id uuid
)
returns boolean as $$
declare
  user_role UUID;
  user_workgroup_id uuid;
begin
  -- Extract workgroup_id from JWT metadata
  user_workgroup_id := (auth.jwt() -> 'app_metadata' ->> 'workgroup_id')::uuid;

  if user_workgroup_id is null or user_workgroup_id != row_workgroup_id then
    return false;
  end if;

  select role_id into user_role from public.user_profiles where id = (auth.uid())::uuid;

  RETURN EXISTS (
    SELECT 1
    FROM public.role_permissions rp
    WHERE rp.permission = requested_permission
      AND rp.role_id = user_role
  );
end;
$$ language plpgsql stable security definer set search_path = '';

CREATE OR REPLACE FUNCTION public.can_read_permission(role_id_param uuid)
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM public.user_profiles up
    WHERE up.id = (auth.uid())::uuid
      AND up.role_id = role_id_param
  );
END;
$$ LANGUAGE plpgsql stable security definer set search_path = '';

create policy "User can read his permissions" on public.role_permissions for select to authenticated using ( (SELECT public.can_read_permission(role_permissions.role_id) ) );

create policy "Allow authorized list users" on public.user_profiles for select to authenticated using ( (SELECT public.has_permission('users.list', workgroup_id)) );

create policy "Allow authorized list roles" on public.roles for select to authenticated using ( (SELECT public.has_permission('users.update', workgroup_id)) );
