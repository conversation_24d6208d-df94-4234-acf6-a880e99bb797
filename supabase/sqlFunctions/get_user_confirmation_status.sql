CREATE OR REPLACE FUNCTION get_user_confirmation_status(user_id UUID)
RETURNS JSON AS $$
DECLARE
  has_permission_to_list_users boolean;
  email_confirmed_date timestamp;
  requested_user_workgroup_id uuid;
BEGIN
    SELECT workgroup_id INTO requested_user_workgroup_id FROM public.user_profiles WHERE id = user_id;    
    SELECT has_permission('users.list', requested_user_workgroup_id) INTO has_permission_to_list_users;

    IF has_permission_to_list_users IS NULL THEN
        RETURN json_build_object(
            'error', 401
        );
    END IF;
  
    RETURN json_build_object(
        'confirmed', (SELECT email_confirmed_at FROM auth.users WHERE id = user_id) IS NOT NULL
    );
  
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
