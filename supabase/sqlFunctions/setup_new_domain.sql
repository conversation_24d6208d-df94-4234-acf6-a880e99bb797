CREATE OR REPLACE FUNCTION setup_new_domain(
  invitation_code TEXT,
  subdomain TEXT,
  company_name TEXT,
  email TEXT,
  user_id UUID
) RETURNS JSON AS $$
DECLARE
  client_result JSON;
  workgroup_result JSON;
  profile_result JSON;
  mark_result JSON;
  client_id UUID;
  workgroup_id UUID;
  admin_role JSON;
  role_id UUID;
BEGIN
  -- Start explicit transaction
  BEGIN  
    SELECT public.find_or_create_client(company_name) INTO client_result;
    
    IF NOT (client_result->>'success')::boolean THEN
      RAISE EXCEPTION 'Client creation failed %', client_result->>'error';
    END IF;
    
    client_id := (client_result->>'client_id')::uuid;
    
    SELECT public.create_workgroup(subdomain, client_id) INTO workgroup_result;
    
    IF NOT (workgroup_result->>'success')::boolean THEN
      RAISE EXCEPTION 'Workgroup creation failed %', workgroup_result->>'error';
    END IF;
    
    workgroup_id := (workgroup_result->>'workgroup_id')::uuid;

    SELECT public.create_default_admin_role(workgroup_id) INTO admin_role;

    IF NOT (admin_role->>'success')::boolean THEN
      RAISE EXCEPTION 'Default admin role creation failed %', admin_role->>'error';
    END IF;

    role_id := (admin_role->>'role_id')::uuid;

    SELECT public.create_user_profile(user_id, workgroup_id, role_id, '', '', email) INTO profile_result;
    
    IF NOT (profile_result->>'success')::boolean THEN
      RAISE EXCEPTION 'User profile creation failed %', profile_result->>'error';
    END IF;
    
    SELECT public.mark_invitation_used(invitation_code::UUID, user_id) INTO mark_result;
    
    IF NOT (mark_result->>'success')::boolean THEN
      RAISE EXCEPTION 'Invitation code activation failed %', mark_result->>'error';
    END IF;
    
    RAISE NOTICE 'Successfully registered workgroup admin for user %', user_id;

    RETURN json_build_object(
        'success', true,
        'message', 'Successfully registered workgroup admin',
        'workgroup_id', workgroup_id,
        'client_id', client_id,
        'user_id', user_id
      );
  EXCEPTION 
    WHEN OTHERS THEN
      RAISE LOG 'Registration error for user %: %', user_id, SQLERRM;
      RAISE;
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
